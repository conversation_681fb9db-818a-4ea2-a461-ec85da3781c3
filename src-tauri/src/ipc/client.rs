//! 守护进程 IPC 客户端模块

use crate::ipc::{IpcMessage, IpcResponse, IpcError, IpcResult};
use std::sync::atomic::{AtomicU32, Ordering};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{Mutex, mpsc};
use tokio::net::TcpStream;
use tokio::io::{AsyncReadExt, AsyncWriteExt};

#[cfg(unix)]
use tokio::net::UnixStream;

/// 传输类型
#[derive(Debug, Clone, PartialEq)]
pub enum TransportType {
    Tcp,
    #[cfg(unix)]
    UnixSocket,
}

/// 守护进程连接配置
#[derive(Debug, Clone)]
pub struct DaemonConnectionConfig {
    /// 守护进程地址
    pub address: String,
    /// 传输类型
    pub transport_type: TransportType,
    /// 连接超时时间
    pub timeout: Duration,
    /// 最大重连尝试次数
    pub max_reconnect_attempts: u32,
    /// 重连延迟
    pub reconnect_delay: Duration,
    /// 消息超时时间
    pub message_timeout: Duration,
    /// 启用心跳
    pub enable_heartbeat: bool,
    /// 心跳间隔
    pub heartbeat_interval: Duration,
}

impl Default for DaemonConnectionConfig {
    fn default() -> Self {
        Self {
            #[cfg(unix)]
            address: "/tmp/secure-password-daemon.sock".to_string(),
            #[cfg(not(unix))]
            address: "127.0.0.1:8080".to_string(),
            #[cfg(unix)]
            transport_type: TransportType::UnixSocket,
            #[cfg(not(unix))]
            transport_type: TransportType::Tcp,
            timeout: Duration::from_secs(30),
            max_reconnect_attempts: 5,
            reconnect_delay: Duration::from_secs(2),
            message_timeout: Duration::from_secs(10),
            enable_heartbeat: true,
            heartbeat_interval: Duration::from_secs(30),
        }
    }
}

/// IPC 连接接口
#[async_trait::async_trait]
pub trait IpcConnection: Send + Sync {
    /// 发送数据
    async fn send(&mut self, data: &[u8]) -> IpcResult<()>;
    
    /// 接收数据
    async fn receive(&mut self) -> IpcResult<Vec<u8>>;
    
    /// 关闭连接
    async fn close(&mut self) -> IpcResult<()>;
    
    /// 获取连接ID
    fn connection_id(&self) -> &str;
    
    /// 检查连接是否活跃
    fn is_active(&self) -> bool;
}

/// TCP 连接实现
pub struct TcpConnection {
    stream: TcpStream,
    connection_id: String,
    is_active: bool,
}

impl TcpConnection {
    /// 连接到指定地址
    pub async fn connect(address: &str) -> IpcResult<Self> {
        let stream = TcpStream::connect(address)
            .await
            .map_err(|e| IpcError::ConnectionError {
                error: format!("TCP连接失败: {}", e)
            })?;

        let connection_id = format!("tcp_{}", uuid::Uuid::new_v4());

        Ok(Self {
            stream,
            connection_id,
            is_active: true,
        })
    }
}

#[async_trait::async_trait]
impl IpcConnection for TcpConnection {
    async fn send(&mut self, data: &[u8]) -> IpcResult<()> {
        if !self.is_active {
            return Err(IpcError::ConnectionNotAvailable);
        }
        
        // 发送数据长度（4字节）
        let len = data.len() as u32;
        self.stream.write_all(&len.to_be_bytes()).await
            .map_err(|e| IpcError::NetworkError {
                error: format!("发送长度失败: {}", e)
            })?;

        // 发送数据
        self.stream.write_all(data).await
            .map_err(|e| IpcError::NetworkError {
                error: format!("发送数据失败: {}", e)
            })?;

        self.stream.flush().await
            .map_err(|e| IpcError::NetworkError {
                error: format!("刷新缓冲区失败: {}", e)
            })?;
        
        Ok(())
    }
    
    async fn receive(&mut self) -> IpcResult<Vec<u8>> {
        if !self.is_active {
            return Err(IpcError::ConnectionNotAvailable);
        }
        
        // 读取数据长度（4字节）
        let mut len_bytes = [0u8; 4];
        self.stream.read_exact(&mut len_bytes).await
            .map_err(|e| IpcError::NetworkError {
                error: format!("读取长度失败: {}", e)
            })?;

        let len = u32::from_be_bytes(len_bytes) as usize;

        // 检查数据长度是否合理
        if len > 10 * 1024 * 1024 { // 10MB 限制
            return Err(IpcError::InvalidMessageFormat {
                message: "消息过大".to_string()
            });
        }

        // 读取数据
        let mut data = vec![0u8; len];
        self.stream.read_exact(&mut data).await
            .map_err(|e| IpcError::NetworkError {
                error: format!("读取数据失败: {}", e)
            })?;
        
        Ok(data)
    }
    
    async fn close(&mut self) -> IpcResult<()> {
        self.is_active = false;
        self.stream.shutdown().await
            .map_err(|e| IpcError::NetworkError {
                error: format!("关闭连接失败: {}", e)
            })?;
        Ok(())
    }
    
    fn connection_id(&self) -> &str {
        &self.connection_id
    }
    
    fn is_active(&self) -> bool {
        self.is_active
    }
}

/// Unix Socket 连接实现 (仅Unix系统)
#[cfg(unix)]
pub struct UnixSocketConnection {
    stream: UnixStream,
    connection_id: String,
    is_active: bool,
}

#[cfg(unix)]
impl UnixSocketConnection {
    /// 连接到指定Unix Socket路径
    pub async fn connect(socket_path: &str) -> IpcResult<Self> {
        let stream = UnixStream::connect(socket_path)
            .await
            .map_err(|e| IpcError::ConnectionError {
                error: format!("Unix Socket连接失败: {}", e)
            })?;

        let connection_id = format!("unix_{}", uuid::Uuid::new_v4());

        Ok(Self {
            stream,
            connection_id,
            is_active: true,
        })
    }
}

#[cfg(unix)]
#[async_trait::async_trait]
impl IpcConnection for UnixSocketConnection {
    async fn send(&mut self, data: &[u8]) -> IpcResult<()> {
        if !self.is_active {
            return Err(IpcError::ConnectionNotAvailable);
        }

        // 发送数据长度（4字节）
        let len = data.len() as u32;
        self.stream.write_all(&len.to_be_bytes()).await
            .map_err(|e| IpcError::NetworkError {
                error: format!("发送长度失败: {}", e)
            })?;

        // 发送数据
        self.stream.write_all(data).await
            .map_err(|e| IpcError::NetworkError {
                error: format!("发送数据失败: {}", e)
            })?;

        self.stream.flush().await
            .map_err(|e| IpcError::NetworkError {
                error: format!("刷新缓冲区失败: {}", e)
            })?;

        Ok(())
    }

    async fn receive(&mut self) -> IpcResult<Vec<u8>> {
        if !self.is_active {
            return Err(IpcError::ConnectionNotAvailable);
        }

        // 读取数据长度（4字节）
        let mut len_bytes = [0u8; 4];
        self.stream.read_exact(&mut len_bytes).await
            .map_err(|e| IpcError::NetworkError {
                error: format!("读取长度失败: {}", e)
            })?;

        let len = u32::from_be_bytes(len_bytes) as usize;

        // 检查数据长度是否合理
        if len > 10 * 1024 * 1024 { // 10MB 限制
            return Err(IpcError::InvalidMessageFormat {
                message: "消息过大".to_string()
            });
        }

        // 读取数据
        let mut data = vec![0u8; len];
        self.stream.read_exact(&mut data).await
            .map_err(|e| IpcError::NetworkError {
                error: format!("读取数据失败: {}", e)
            })?;

        Ok(data)
    }

    async fn close(&mut self) -> IpcResult<()> {
        self.is_active = false;
        self.stream.shutdown().await
            .map_err(|e| IpcError::NetworkError {
                error: format!("关闭连接失败: {}", e)
            })?;
        Ok(())
    }

    fn connection_id(&self) -> &str {
        &self.connection_id
    }

    fn is_active(&self) -> bool {
        self.is_active
    }
}

/// 守护进程 IPC 客户端
pub struct DaemonIpcClient {
    connection: Arc<Mutex<Option<Box<dyn IpcConnection>>>>,
    config: DaemonConnectionConfig,
    reconnect_attempts: AtomicU32,
    #[allow(dead_code)]
    message_sender: Option<mpsc::UnboundedSender<IpcMessage>>,
    #[allow(dead_code)]
    response_receiver: Option<mpsc::UnboundedReceiver<IpcResponse>>,
}

impl DaemonIpcClient {
    /// 连接到守护进程
    pub async fn connect(address: &str) -> IpcResult<Self> {
        let config = DaemonConnectionConfig {
            address: address.to_string(),
            ..Default::default()
        };
        
        let connection = Self::establish_connection(&config).await?;
        
        Ok(Self {
            connection: Arc::new(Mutex::new(Some(connection))),
            config,
            reconnect_attempts: AtomicU32::new(0),
            message_sender: None,
            response_receiver: None,
        })
    }
    
    /// 使用配置连接
    pub async fn connect_with_config(config: DaemonConnectionConfig) -> IpcResult<Self> {
        let connection = Self::establish_connection(&config).await?;
        
        Ok(Self {
            connection: Arc::new(Mutex::new(Some(connection))),
            config,
            reconnect_attempts: AtomicU32::new(0),
            message_sender: None,
            response_receiver: None,
        })
    }
    
    /// 建立连接
    async fn establish_connection(config: &DaemonConnectionConfig) -> IpcResult<Box<dyn IpcConnection>> {
        match config.transport_type {
            TransportType::Tcp => {
                let tcp_conn = TcpConnection::connect(&config.address).await?;
                Ok(Box::new(tcp_conn))
            }
            #[cfg(unix)]
            TransportType::UnixSocket => {
                let unix_conn = UnixSocketConnection::connect(&config.address).await?;
                Ok(Box::new(unix_conn))
            }
        }
    }
    
    /// 发送消息到守护进程
    pub async fn send_message(&self, message: &IpcMessage) -> IpcResult<()> {
        let mut connection_guard = self.connection.lock().await;
        
        if let Some(connection) = connection_guard.as_mut() {
            let serialized = serde_json::to_vec(message)
                .map_err(|e| IpcError::SerializationError {
                    error: e.to_string()
                })?;
            
            connection.send(&serialized).await?;
            Ok(())
        } else {
            Err(IpcError::ConnectionNotAvailable)
        }
    }
    
    /// 接收来自守护进程的消息
    pub async fn receive_message(&self) -> IpcResult<IpcMessage> {
        let mut connection_guard = self.connection.lock().await;
        
        if let Some(connection) = connection_guard.as_mut() {
            let data = connection.receive().await?;
            let message: IpcMessage = serde_json::from_slice(&data)
                .map_err(|e| IpcError::DeserializationError {
                    error: e.to_string()
                })?;
            Ok(message)
        } else {
            Err(IpcError::ConnectionNotAvailable)
        }
    }
    
    /// 发送响应
    pub async fn send_response(&self, response: &IpcResponse) -> IpcResult<()> {
        let mut connection_guard = self.connection.lock().await;
        
        if let Some(connection) = connection_guard.as_mut() {
            let serialized = serde_json::to_vec(response)
                .map_err(|e| IpcError::SerializationError {
                    error: e.to_string()
                })?;
            
            connection.send(&serialized).await?;
            Ok(())
        } else {
            Err(IpcError::ConnectionNotAvailable)
        }
    }
    
    /// 发送请求并等待响应
    pub async fn send_request(&self, message: &IpcMessage) -> IpcResult<IpcResponse> {
        // 发送消息
        self.send_message(message).await?;
        
        // 等待响应（简化实现，实际应该根据 message_id 匹配）
        let timeout = tokio::time::timeout(
            self.config.message_timeout,
            self.receive_response(&message.message_id)
        );
        
        match timeout.await {
            Ok(result) => result,
            Err(_) => Err(IpcError::TimeoutError {
                operation: "等待响应超时".to_string()
            }),
        }
    }
    
    /// 接收特定消息ID的响应
    async fn receive_response(&self, _message_id: &str) -> IpcResult<IpcResponse> {
        // 简化实现：直接接收下一个消息并假设它是响应
        // 实际实现应该维护一个响应映射表
        let mut connection_guard = self.connection.lock().await;
        
        if let Some(connection) = connection_guard.as_mut() {
            let data = connection.receive().await?;
            let response: IpcResponse = serde_json::from_slice(&data)
                .map_err(|e| IpcError::DeserializationError {
                    error: e.to_string()
                })?;
            Ok(response)
        } else {
            Err(IpcError::ConnectionNotAvailable)
        }
    }
    
    /// 重连到守护进程
    pub async fn reconnect(&self) -> IpcResult<()> {
        let attempts = self.reconnect_attempts.load(Ordering::SeqCst);
        
        if attempts >= self.config.max_reconnect_attempts {
            return Err(IpcError::MaxReconnectAttemptsExceeded);
        }
        
        // 等待重连延迟
        tokio::time::sleep(self.config.reconnect_delay).await;
        
        // 尝试重新建立连接
        match Self::establish_connection(&self.config).await {
            Ok(new_connection) => {
                let mut connection_guard = self.connection.lock().await;
                *connection_guard = Some(new_connection);
                self.reconnect_attempts.store(0, Ordering::SeqCst);
                log::info!("成功重连到守护进程");
                Ok(())
            }
            Err(e) => {
                self.reconnect_attempts.fetch_add(1, Ordering::SeqCst);
                log::error!("重连失败 (尝试 {}/{}): {}", 
                    attempts + 1, self.config.max_reconnect_attempts, e);
                Err(e)
            }
        }
    }
    
    /// 检查连接状态
    pub async fn is_connected(&self) -> bool {
        let connection_guard = self.connection.lock().await;
        connection_guard.as_ref()
            .map(|conn| conn.is_active())
            .unwrap_or(false)
    }
    
    /// 关闭连接
    pub async fn close(&self) -> IpcResult<()> {
        let mut connection_guard = self.connection.lock().await;
        
        if let Some(mut connection) = connection_guard.take() {
            connection.close().await?;
        }
        
        Ok(())
    }
    
    /// 发送心跳
    pub async fn send_heartbeat(&self) -> IpcResult<()> {
        let heartbeat = IpcMessage::new_simple(
            "ping".to_string(),
            serde_json::json!({"timestamp": chrono::Utc::now().timestamp()})
        );

        self.send_message(&heartbeat).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_daemon_connection_config_default() {
        let config = DaemonConnectionConfig::default();
        assert!(!config.address.is_empty());
        assert!(config.timeout.as_secs() > 0);
        assert!(config.max_reconnect_attempts > 0);
        assert!(config.enable_heartbeat);
    }

    #[tokio::test]
    async fn test_tcp_connection_creation() {
        // 这个测试需要实际的TCP服务器，所以只测试连接ID生成
        let connection_id = format!("tcp_{}", uuid::Uuid::new_v4());
        assert!(connection_id.starts_with("tcp_"));
        assert!(connection_id.len() > 4);
    }

    #[test]
    fn test_reconnect_attempts() {
        let client = DaemonIpcClient {
            connection: Arc::new(Mutex::new(None)),
            config: DaemonConnectionConfig::default(),
            reconnect_attempts: AtomicU32::new(0),
            message_sender: None,
            response_receiver: None,
        };
        
        assert_eq!(client.reconnect_attempts.load(Ordering::SeqCst), 0);
        client.reconnect_attempts.fetch_add(1, Ordering::SeqCst);
        assert_eq!(client.reconnect_attempts.load(Ordering::SeqCst), 1);
    }
}
