//! IPC 协议定义模块
//!
//! 定义进程间通信的消息格式、错误类型和版本管理

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use uuid::Uuid;
use thiserror::Error;

/// IPC 协议版本
pub const IPC_PROTOCOL_VERSION: u32 = 1;

/// IPC 消息定义
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct IpcMessage {
    /// 协议版本
    pub version: u32,
    /// 消息ID (用于请求-响应配对)
    pub message_id: String,
    /// 消息类型
    pub message_type: IpcMessageType,
    /// 消息负载
    pub payload: serde_json::Value,
    /// 时间戳 (Unix时间戳)
    pub timestamp: u64,
    /// 消息来源
    pub source: String,
    /// 消息目标 (可选)
    pub target: Option<String>,
    /// 是否需要响应
    pub response_required: bool,
    /// 消息优先级
    pub priority: MessagePriority,
    /// 自定义头部信息
    pub headers: HashMap<String, String>,
}

/// IPC 消息类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum IpcMessageType {
    // 系统控制消息
    /// Ping 消息 (健康检查)
    Ping,
    /// Pong 消息 (Ping 响应)
    Pong,
    /// 关闭连接
    Close,
    /// 心跳消息
    Heartbeat,

    // 应用管理消息
    /// 启动应用
    LaunchApp,
    /// 关闭应用
    ShutdownApp,
    /// 应用状态查询
    AppStatus,
    /// 应用健康检查
    AppHealthCheck,

    // Native Messaging 代理消息
    /// 浏览器请求
    BrowserRequest,
    /// 浏览器响应
    BrowserResponse,
    /// 扩展注册
    ExtensionRegister,
    /// 扩展注销
    ExtensionUnregister,

    // 安全验证消息
    /// 认证请求
    AuthRequest,
    /// 认证响应
    AuthResponse,
    /// 权限验证
    PermissionCheck,
    /// 安全事件通知
    SecurityEvent,

    // 监控和统计消息
    /// 指标上报
    MetricsReport,
    /// 健康状态报告
    HealthReport,
    /// 日志消息
    LogMessage,
    /// 告警消息
    AlertMessage,

    // 通用消息
    /// 成功响应
    Success,
    /// 错误响应
    Error,
    /// 通知消息
    Notification,
    /// 自定义消息
    Custom(String),

    // 凭证管理 (兼容旧版本)
    GetCredentials,
    SaveCredential,
    UpdateCredential,
    DeleteCredential,
    SearchCredentials,

    // 设置管理 (兼容旧版本)
    GetSettings,
    UpdateSettings,
    ResetSettings,

    // 同步相关 (兼容旧版本)
    SyncRequest,
    SyncResponse,
    SyncStatus,
}

impl std::fmt::Display for IpcMessageType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let s = match self {
            IpcMessageType::Ping => "ping",
            IpcMessageType::Pong => "pong",
            IpcMessageType::Close => "close",
            IpcMessageType::Heartbeat => "heartbeat",
            IpcMessageType::LaunchApp => "launch_app",
            IpcMessageType::ShutdownApp => "shutdown_app",
            IpcMessageType::AppStatus => "app_status",
            IpcMessageType::AppHealthCheck => "app_health_check",
            IpcMessageType::BrowserRequest => "browser_request",
            IpcMessageType::BrowserResponse => "browser_response",
            IpcMessageType::ExtensionRegister => "extension_register",
            IpcMessageType::ExtensionUnregister => "extension_unregister",
            IpcMessageType::AuthRequest => "auth_request",
            IpcMessageType::AuthResponse => "auth_response",
            IpcMessageType::PermissionCheck => "permission_check",
            IpcMessageType::SecurityEvent => "security_event",
            IpcMessageType::MetricsReport => "metrics_report",
            IpcMessageType::HealthReport => "health_report",
            IpcMessageType::LogMessage => "log_message",
            IpcMessageType::AlertMessage => "alert_message",
            IpcMessageType::Success => "success",
            IpcMessageType::Error => "error",
            IpcMessageType::Notification => "notification",
            IpcMessageType::Custom(s) => s,
            IpcMessageType::GetCredentials => "get_credentials",
            IpcMessageType::SaveCredential => "save_credential",
            IpcMessageType::UpdateCredential => "update_credential",
            IpcMessageType::DeleteCredential => "delete_credential",
            IpcMessageType::SearchCredentials => "search_credentials",
            IpcMessageType::GetSettings => "get_settings",
            IpcMessageType::UpdateSettings => "update_settings",
            IpcMessageType::ResetSettings => "reset_settings",
            IpcMessageType::SyncRequest => "sync_request",
            IpcMessageType::SyncResponse => "sync_response",
            IpcMessageType::SyncStatus => "sync_status",
        };
        write!(f, "{}", s)
    }
}

impl IpcMessage {
    /// 创建新的IPC消息
    pub fn new(
        message_type: IpcMessageType,
        payload: serde_json::Value,
        source: String,
    ) -> Self {
        Self {
            version: IPC_PROTOCOL_VERSION,
            message_id: Uuid::new_v4().to_string(),
            message_type,
            payload,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source,
            target: None,
            response_required: false,
            priority: MessagePriority::Normal,
            headers: HashMap::new(),
        }
    }

    /// 创建需要响应的消息
    pub fn new_request(
        message_type: IpcMessageType,
        payload: serde_json::Value,
        source: String,
        target: String,
    ) -> Self {
        let mut message = Self::new(message_type, payload, source);
        message.target = Some(target);
        message.response_required = true;
        message
    }

    /// 创建简单消息（兼容旧版本API）
    pub fn new_simple(message_type_str: String, payload: serde_json::Value) -> Self {
        let message_type = match message_type_str.as_str() {
            "ping" => IpcMessageType::Ping,
            "pong" => IpcMessageType::Pong,
            "get_credentials" => IpcMessageType::GetCredentials,
            "save_credential" => IpcMessageType::SaveCredential,
            "health_check" => IpcMessageType::HealthReport,
            _ => IpcMessageType::Custom(message_type_str),
        };

        Self::new(message_type, payload, "tauri_client".to_string())
    }

    /// 设置消息目标
    pub fn with_target(mut self, target: String) -> Self {
        self.target = Some(target);
        self
    }

    /// 设置消息优先级
    pub fn with_priority(mut self, priority: MessagePriority) -> Self {
        self.priority = priority;
        self
    }

    /// 添加头部信息
    pub fn with_header(mut self, key: String, value: String) -> Self {
        self.headers.insert(key, value);
        self
    }
    
    /// 验证消息格式
    pub fn validate(&self) -> Result<(), String> {
        if self.message_id.is_empty() {
            return Err("消息ID不能为空".to_string());
        }
        
        if self.message_type.is_empty() {
            return Err("消息类型不能为空".to_string());
        }
        
        if self.timestamp == 0 {
            return Err("时间戳不能为0".to_string());
        }
        
        Ok(())
    }
    
    /// 检查消息是否过期
    pub fn is_expired(&self, timeout_ms: u64) -> bool {
        let now = chrono::Utc::now().timestamp_millis() as u64;
        now > self.timestamp + timeout_ms
    }
}

/// IPC 响应消息
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct IpcResponse {
    /// 原始请求的消息ID
    pub request_id: String,
    /// 响应状态
    pub status: ResponseStatus,
    /// 响应数据
    pub data: serde_json::Value,
    /// 错误信息 (如果有)
    pub error: Option<String>,
    /// 响应时间戳
    pub timestamp: u64,
    /// 响应来源
    pub source: String,
}

impl IpcResponse {
    /// 创建成功响应
    pub fn success(request_id: String, data: serde_json::Value) -> Self {
        Self {
            request_id,
            status: ResponseStatus::Success,
            data,
            error: None,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source: "tauri_client".to_string(),
        }
    }

    /// 创建错误响应
    pub fn error(request_id: String, error: String) -> Self {
        Self {
            request_id,
            status: ResponseStatus::Error,
            data: serde_json::Value::Null,
            error: Some(error),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source: "tauri_client".to_string(),
        }
    }

    /// 检查响应是否成功
    pub fn is_success(&self) -> bool {
        self.status == ResponseStatus::Success
    }

    /// 兼容旧版本API - 获取payload
    pub fn payload(&self) -> &serde_json::Value {
        &self.data
    }

    /// 兼容旧版本API - 设置处理时间（暂时忽略）
    pub fn with_processing_time(self, _processing_time_ms: u64) -> Self {
        self
    }
}

/// 消息优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum MessagePriority {
    /// 低优先级
    Low = 0,
    /// 普通优先级
    Normal = 1,
    /// 高优先级
    High = 2,
    /// 紧急优先级
    Critical = 3,
}

impl Default for MessagePriority {
    fn default() -> Self {
        MessagePriority::Normal
    }
}

/// 响应状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ResponseStatus {
    /// 成功
    Success,
    /// 失败
    Error,
    /// 超时
    Timeout,
    /// 未找到
    NotFound,
    /// 权限不足
    PermissionDenied,
    /// 服务不可用
    ServiceUnavailable,
}



impl IpcMessageType {
    /// 检查是否需要认证
    pub fn requires_auth(&self) -> bool {
        match self {
            IpcMessageType::GetCredentials |
            IpcMessageType::SaveCredential |
            IpcMessageType::UpdateCredential |
            IpcMessageType::DeleteCredential |
            IpcMessageType::SearchCredentials |
            IpcMessageType::GetSettings |
            IpcMessageType::UpdateSettings |
            IpcMessageType::ResetSettings |
            IpcMessageType::SyncRequest => true,
            _ => false,
        }
    }

    /// 获取默认超时时间（毫秒）
    pub fn default_timeout_ms(&self) -> u64 {
        match self {
            IpcMessageType::AppHealthCheck | IpcMessageType::Ping => 5000,
            IpcMessageType::GetCredentials | IpcMessageType::SearchCredentials => 10000,
            IpcMessageType::SaveCredential | IpcMessageType::UpdateCredential => 15000,
            IpcMessageType::SyncRequest => 30000,
            _ => 10000,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ipc_message_creation() {
        let payload = serde_json::json!({"test": "data"});
        let message = IpcMessage::new_simple("test_type".to_string(), payload.clone());

        assert!(!message.message_id.is_empty());
        assert_eq!(message.message_type, IpcMessageType::Custom("test_type".to_string()));
        assert_eq!(message.payload, payload);
        assert!(!message.response_required);
        assert_eq!(message.priority, MessagePriority::Normal);
    }

    #[test]
    fn test_ipc_message_with_response() {
        let payload = serde_json::json!({"test": "data"});
        let message = IpcMessage::new_with_response("test_type".to_string(), payload);
        
        assert!(message.response_required);
    }

    #[test]
    fn test_ipc_message_validation() {
        let message = IpcMessage::new_simple("test".to_string(), serde_json::Value::Null);
        // 简单验证消息字段不为空
        assert!(!message.message_id.is_empty());
        assert!(!message.source.is_empty());
        assert!(message.version > 0);
    }

    #[test]
    fn test_ipc_response_creation() {
        let payload = serde_json::json!({"result": "success"});
        let response = IpcResponse::success("test_id".to_string(), payload.clone());
        
        assert_eq!(response.request_id, "test_id");
        assert_eq!(response.status, ResponseStatus::Success);
        assert_eq!(response.payload, payload);
        assert!(response.error.is_none());
    }

    #[test]
    fn test_error_response() {
        let response = IpcResponse::error("test_id", "test error");
        
        assert_eq!(response.request_id, "test_id");
        assert_eq!(response.status, ResponseStatus::Error);
        assert_eq!(response.error, Some("test error".to_string()));
    }

    #[test]
    fn test_message_priority_ordering() {
        assert!(MessagePriority::Low < MessagePriority::Normal);
        assert!(MessagePriority::Normal < MessagePriority::High);
        assert!(MessagePriority::High < MessagePriority::Critical);
    }

    #[test]
    fn test_message_type_conversion() {
        let msg_type = IpcMessageType::GetCredentials;
        assert_eq!(msg_type.as_str(), "get_credentials");
        assert_eq!(IpcMessageType::from_str("get_credentials"), msg_type);
    }

    #[test]
    fn test_message_type_auth_requirement() {
        assert!(IpcMessageType::GetCredentials.requires_auth());
        assert!(IpcMessageType::SaveCredential.requires_auth());
        assert!(!IpcMessageType::HealthCheck.requires_auth());
        assert!(!IpcMessageType::Ping.requires_auth());
    }

    #[test]
    fn test_message_type_timeout() {
        assert_eq!(IpcMessageType::HealthCheck.default_timeout_ms(), 5000);
        assert_eq!(IpcMessageType::GetCredentials.default_timeout_ms(), 10000);
        assert_eq!(IpcMessageType::SyncRequest.default_timeout_ms(), 30000);
    }
}
