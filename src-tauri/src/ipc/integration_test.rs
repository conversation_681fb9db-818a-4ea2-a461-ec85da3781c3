//! IPC 通信集成测试
//! 
//! 测试守护进程和Tauri客户端之间的IPC通信功能

use crate::ipc::{IpcMessage, IpcResponse, IpcError, IpcResult, IpcMessageType, ResponseStatus};
use crate::ipc::client::{DaemonIpcClient, DaemonConnectionConfig, TransportType};
use std::time::Duration;
use tokio::time::sleep;

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试协议兼容性
    #[test]
    fn test_protocol_compatibility() {
        // 测试消息创建
        let message = IpcMessage::new_simple(
            "ping".to_string(),
            serde_json::json!({"test": "data"})
        );
        
        // 验证消息字段
        assert!(!message.message_id.is_empty());
        assert_eq!(message.version, 1);
        assert_eq!(message.message_type, IpcMessageType::Ping);
        assert_eq!(message.source, "tauri_client");
        
        // 测试序列化
        let serialized = serde_json::to_vec(&message).unwrap();
        assert!(!serialized.is_empty());
        
        // 测试反序列化
        let deserialized: IpcMessage = serde_json::from_slice(&serialized).unwrap();
        assert_eq!(deserialized.message_id, message.message_id);
        assert_eq!(deserialized.message_type, message.message_type);
    }

    /// 测试响应创建和序列化
    #[test]
    fn test_response_serialization() {
        let response = IpcResponse::success(
            "test-id".to_string(),
            serde_json::json!({"result": "ok"})
        );
        
        assert_eq!(response.request_id, "test-id");
        assert_eq!(response.status, ResponseStatus::Success);
        assert!(response.is_success());
        
        // 测试序列化
        let serialized = serde_json::to_vec(&response).unwrap();
        let deserialized: IpcResponse = serde_json::from_slice(&serialized).unwrap();
        assert_eq!(deserialized.request_id, response.request_id);
        assert_eq!(deserialized.status, response.status);
    }

    /// 测试错误类型序列化
    #[test]
    fn test_error_serialization() {
        let error = IpcError::ConnectionError {
            error: "测试错误".to_string()
        };
        
        // 测试序列化
        let serialized = serde_json::to_vec(&error).unwrap();
        let deserialized: IpcError = serde_json::from_slice(&serialized).unwrap();
        assert_eq!(deserialized, error);
    }

    /// 测试连接配置
    #[test]
    fn test_connection_config() {
        let config = DaemonConnectionConfig::default();
        
        #[cfg(unix)]
        {
            assert_eq!(config.transport_type, TransportType::UnixSocket);
            assert!(config.address.contains(".sock"));
        }
        
        #[cfg(not(unix))]
        {
            assert_eq!(config.transport_type, TransportType::Tcp);
            assert!(config.address.contains(":"));
        }
        
        assert!(config.timeout.as_secs() > 0);
        assert!(config.max_reconnect_attempts > 0);
    }

    /// 测试消息类型转换
    #[test]
    fn test_message_type_conversion() {
        let ping_type = IpcMessageType::Ping;
        assert_eq!(ping_type.to_string(), "ping");
        
        let custom_type = IpcMessageType::Custom("test".to_string());
        assert_eq!(custom_type.to_string(), "test");
    }

    /// 模拟IPC通信测试（不需要实际连接）
    #[tokio::test]
    async fn test_message_flow_simulation() {
        // 创建请求消息
        let request = IpcMessage::new_simple(
            "health_check".to_string(),
            serde_json::json!({"timestamp": **********})
        );
        
        // 模拟序列化传输
        let serialized_request = serde_json::to_vec(&request).unwrap();
        let received_request: IpcMessage = serde_json::from_slice(&serialized_request).unwrap();
        
        // 验证请求接收正确
        assert_eq!(received_request.message_id, request.message_id);
        assert_eq!(received_request.message_type, IpcMessageType::HealthReport);
        
        // 创建响应
        let response = IpcResponse::success(
            received_request.message_id,
            serde_json::json!({"status": "healthy"})
        );
        
        // 模拟响应传输
        let serialized_response = serde_json::to_vec(&response).unwrap();
        let received_response: IpcResponse = serde_json::from_slice(&serialized_response).unwrap();
        
        // 验证响应接收正确
        assert_eq!(received_response.request_id, request.message_id);
        assert!(received_response.is_success());
    }

    /// 测试错误响应处理
    #[tokio::test]
    async fn test_error_response_handling() {
        let error_response = IpcResponse::error(
            "test-id".to_string(),
            "测试错误".to_string()
        );
        
        assert_eq!(error_response.status, ResponseStatus::Error);
        assert!(!error_response.is_success());
        assert!(error_response.error.is_some());
        
        // 测试序列化
        let serialized = serde_json::to_vec(&error_response).unwrap();
        let deserialized: IpcResponse = serde_json::from_slice(&serialized).unwrap();
        assert_eq!(deserialized.status, ResponseStatus::Error);
        assert_eq!(deserialized.error, error_response.error);
    }
}

/// 集成测试辅助函数
pub mod test_helpers {
    use super::*;
    
    /// 创建测试用的守护进程配置
    pub fn create_test_daemon_config() -> DaemonConnectionConfig {
        DaemonConnectionConfig {
            #[cfg(unix)]
            address: "/tmp/test-secure-password-daemon.sock".to_string(),
            #[cfg(not(unix))]
            address: "127.0.0.1:18080".to_string(),
            #[cfg(unix)]
            transport_type: TransportType::UnixSocket,
            #[cfg(not(unix))]
            transport_type: TransportType::Tcp,
            timeout: Duration::from_secs(5),
            max_reconnect_attempts: 2,
            reconnect_delay: Duration::from_millis(500),
            message_timeout: Duration::from_secs(3),
            enable_heartbeat: false,
            heartbeat_interval: Duration::from_secs(10),
        }
    }
    
    /// 创建测试消息
    pub fn create_test_message(msg_type: &str, payload: serde_json::Value) -> IpcMessage {
        IpcMessage::new_simple(msg_type.to_string(), payload)
    }
    
    /// 验证消息格式
    pub fn validate_message_format(message: &IpcMessage) -> bool {
        !message.message_id.is_empty() &&
        message.version > 0 &&
        !message.source.is_empty()
    }
    
    /// 验证响应格式
    pub fn validate_response_format(response: &IpcResponse) -> bool {
        !response.request_id.is_empty() &&
        response.timestamp > 0 &&
        !response.source.is_empty()
    }
}
