# Secure Password 守护进程配置文件
# 企业级密码管理守护进程配置

[service]
name = "secure-password-daemon"
display_name = "Secure Password Daemon"
description = "企业级密码管理守护进程 - 负责Native Messaging代理、IPC通信和应用管理"
auto_start = true
# working_directory = "/var/lib/secure-password"  # Linux/macOS
# working_directory = "%PROGRAMDATA%\\SecurePassword"  # Windows

[ipc]
transport = "Auto"  # Auto, Tcp, UnixSocket, NamedPipe
bind_address = "127.0.0.1"
port = 8080
max_connections = 100
connection_timeout = 30

[native_messaging]
enabled = true
host_name = "com.securepassword.host"
supported_browsers = ["chrome", "firefox", "edge", "safari"]
extension_whitelist = [
    # 在这里添加允许的扩展ID
    # "abcdefghijklmnopqrstuvwxyz123456"
]

[app_manager]
app_path = "/usr/bin/true"  # 使用一个存在的可执行文件作为占位符
startup_timeout = 60
health_check_interval = 30
max_restart_attempts = 3

[security]
enabled = true
encryption_algorithm = "AES-256-GCM"
key_length = 256

[monitoring]
enabled = true
metrics_interval = 60
monitoring_port = 9090

[logging]
level = "info"  # trace, debug, info, warn, error
file_path = "daemon.log"
console = true
format = "Text"  # Text, Json

# 平台特定配置示例

# Windows 特定配置
[windows]
service_account = "LocalService"
log_path = "%PROGRAMDATA%\\SecurePassword\\logs"

# macOS 特定配置
[macos]
user = "_securepassword"
group = "_securepassword"
log_path = "/usr/local/var/log/secure-password"

# Linux 特定配置
[linux]
user = "secure-password"
group = "secure-password"
log_path = "/var/log/secure-password"
systemd_notify = true
