//! 端到端完整消息流转集成测试
//! 
//! 测试完整的浏览器扩展到 Tauri 主应用的消息流转链路：
//! 浏览器扩展 → Native Messaging → 守护进程 → Tauri 客户端

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde_json::json;
use tracing::{info, error, debug};

use secure_password_daemon::{
    daemon_core::SecurePasswordDaemon,
    config::DaemonConfig,
    ipc::{
        client::{IpcClient, ClientConfig, ClientState},
        server::{IpcServer, ServerConfig, MessageHandler, ServerEvent},
        protocol::{IpcMessage, IpcMessageType, IpcResponse},
        transport::TransportType,
    },
    native_messaging::{
        protocol::{NativeMessage, ProtocolVersion},
        host::{NativeMessagingHost, HostConfig},
    },
    app_manager::{AppConfig, AppLifecycleManager},
};

/// 端到端测试套件
pub struct E2EMessageFlowTestSuite {
    /// 守护进程实例
    daemon: Option<SecurePasswordDaemon>,
    /// IPC 服务器
    ipc_server: Option<IpcServer>,
    /// Native Messaging Host
    native_host: Option<NativeMessagingHost>,
    /// 应用管理器
    app_manager: Option<AppLifecycleManager>,
    /// 测试客户端
    test_client: Option<IpcClient>,
    /// 测试配置
    config: E2ETestConfig,
    /// 测试结果
    test_results: Arc<RwLock<HashMap<String, TestResult>>>,
    /// 消息追踪器
    message_tracker: Arc<RwLock<MessageTracker>>,
}

/// 端到端测试配置
#[derive(Debug, Clone)]
pub struct E2ETestConfig {
    /// 守护进程配置
    pub daemon_config: DaemonConfig,
    /// 测试超时时间
    pub test_timeout: Duration,
    /// 最大重试次数
    pub max_retries: u32,
    /// 模拟浏览器扩展ID
    pub extension_id: String,
    /// 测试数据目录
    pub test_data_dir: String,
    /// IPC 服务器端口
    pub ipc_port: u16,
}

/// 测试结果
#[derive(Debug, Clone)]
pub struct TestResult {
    /// 测试名称
    pub name: String,
    /// 是否通过
    pub passed: bool,
    /// 执行时间（毫秒）
    pub duration_ms: u64,
    /// 错误信息
    pub error_message: Option<String>,
    /// 详细信息
    pub details: HashMap<String, serde_json::Value>,
}

/// 消息追踪器
#[derive(Debug, Default)]
pub struct MessageTracker {
    /// 发送的消息
    pub sent_messages: Vec<TrackedMessage>,
    /// 接收的消息
    pub received_messages: Vec<TrackedMessage>,
    /// 消息路由路径
    pub message_routes: HashMap<String, Vec<String>>,
}

/// 追踪的消息
#[derive(Debug, Clone)]
pub struct TrackedMessage {
    /// 消息ID
    pub message_id: String,
    /// 消息类型
    pub message_type: String,
    /// 发送时间
    pub timestamp: Instant,
    /// 消息来源
    pub source: String,
    /// 消息目标
    pub target: Option<String>,
    /// 消息内容摘要
    pub payload_summary: String,
}

impl Default for E2ETestConfig {
    fn default() -> Self {
        Self {
            daemon_config: create_test_daemon_config(),
            test_timeout: Duration::from_secs(30),
            max_retries: 3,
            extension_id: "test-extension-12345".to_string(),
            test_data_dir: "/tmp/e2e_test_data".to_string(),
            ipc_port: 18080,
        }
    }
}

impl E2EMessageFlowTestSuite {
    /// 创建新的端到端测试套件
    pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let config = E2ETestConfig::default();
        
        Ok(Self {
            daemon: None,
            ipc_server: None,
            native_host: None,
            app_manager: None,
            test_client: None,
            config,
            test_results: Arc::new(RwLock::new(HashMap::new())),
            message_tracker: Arc::new(RwLock::new(MessageTracker::default())),
        })
    }

    /// 运行完整的端到端消息流转测试
    pub async fn run_complete_message_flow_test(&mut self) -> Result<HashMap<String, TestResult>, Box<dyn std::error::Error>> {
        info!("🚀 开始运行完整的端到端消息流转测试");

        // 1. 环境准备
        self.test_environment_setup().await?;

        // 2. 启动所有组件
        self.test_component_startup().await?;

        // 3. 建立连接
        self.test_connection_establishment().await?;

        // 4. 端到端消息流转测试
        self.test_browser_to_tauri_message_flow().await?;

        // 5. 业务逻辑测试
        self.test_credential_operations().await?;

        // 6. 错误处理测试
        self.test_error_handling().await?;

        // 7. 性能测试
        self.test_performance_metrics().await?;

        // 8. 清理
        self.test_cleanup().await?;

        let results = self.test_results.read().await.clone();
        self.print_test_summary(&results);
        
        Ok(results)
    }

    /// 测试环境准备
    async fn test_environment_setup(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let test_name = "environment_setup";
        let start_time = Instant::now();
        
        info!("🔧 测试环境准备");

        let result = async {
            // 创建测试数据目录
            tokio::fs::create_dir_all(&self.config.test_data_dir).await?;
            
            // 初始化日志系统
            let _ = tracing_subscriber::fmt()
                .with_max_level(tracing::Level::DEBUG)
                .with_test_writer()
                .try_init();
            
            info!("环境准备完成");
            Ok::<(), Box<dyn std::error::Error>>(())
        }.await;

        self.record_test_result(test_name, &result, start_time).await;
        result
    }

    /// 测试组件启动
    async fn test_component_startup(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let test_name = "component_startup";
        let start_time = Instant::now();
        
        info!("🚀 测试组件启动");

        let result = async {
            // 1. 启动 IPC 服务器
            self.start_ipc_server().await?;
            
            // 2. 创建守护进程（不完全启动）
            self.create_daemon_instance().await?;
            
            // 3. 创建应用管理器
            self.create_app_manager().await?;
            
            // 4. 创建 Native Messaging Host
            self.create_native_messaging_host().await?;
            
            info!("所有组件启动完成");
            Ok::<(), Box<dyn std::error::Error>>(())
        }.await;

        self.record_test_result(test_name, &result, start_time).await;
        result
    }

    /// 启动 IPC 服务器
    async fn start_ipc_server(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let server_config = ServerConfig {
            bind_address: "127.0.0.1".to_string(),
            port: Some(self.config.ipc_port),
            max_connections: 10,
            connection_timeout_ms: 30000,
            message_timeout_ms: 10000,
            heartbeat_interval_ms: 30000,
            transport_config: secure_password_daemon::ipc::transport::TransportConfig {
                transport_type: TransportType::Tcp,
                address: "127.0.0.1".to_string(),
                port: Some(self.config.ipc_port),
                timeout_ms: 30000,
                max_message_size: 1024 * 1024,
                buffer_size: 8192,
            },
        };

        let message_handler = Arc::new(TestMessageHandler::new());
        let mut server = IpcServer::new(server_config).with_message_handler(message_handler);

        // 启动服务器
        server.start().await?;

        self.ipc_server = Some(server);
        info!("IPC 服务器启动成功，端口: {}", self.config.ipc_port);

        Ok(())
    }

    /// 创建守护进程实例
    async fn create_daemon_instance(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let daemon = SecurePasswordDaemon::new(self.config.daemon_config.clone()).await?;
        self.daemon = Some(daemon);
        info!("守护进程实例创建成功");
        Ok(())
    }

    /// 创建应用管理器
    async fn create_app_manager(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let app_config = AppConfig::default();
        let app_manager = AppLifecycleManager::new(app_config);
        self.app_manager = Some(app_manager);
        info!("应用管理器创建成功");
        Ok(())
    }

    /// 创建 Native Messaging Host
    async fn create_native_messaging_host(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let host_config = HostConfig {
            host_name: "com.securepassword.test".to_string(),
            description: "Test Native Messaging Host".to_string(),
            executable_path: std::env::current_exe()
                .unwrap_or_else(|_| "test-daemon".into())
                .to_string_lossy()
                .to_string(),
            allowed_origins: vec![
                "chrome-extension://test-extension/".to_string(),
                "moz-extension://test-extension/".to_string(),
            ],
            allowed_extensions: vec!["test-extension".to_string()],
            ipc_config: ClientConfig {
                server_address: "127.0.0.1".to_string(),
                port: Some(self.config.ipc_port),
                timeout_ms: 5000,
                request_timeout_ms: 10000,
                reconnect_interval_ms: 1000,
                max_reconnect_attempts: 3,
                heartbeat_interval_ms: 30000,
                auto_reconnect: true,
                transport_type: TransportType::Tcp,
                message_buffer_size: 8192,
            },
            protocol_version: ProtocolVersion::V2,
            request_timeout: Duration::from_secs(30),
            max_concurrent_requests: 100,
            debug_mode: true,
            heartbeat_interval: Duration::from_secs(30),
            verify_signatures: false,
            signature_key: None,
        };

        let native_host = NativeMessagingHost::new(host_config).await?;
        self.native_host = Some(native_host);
        info!("Native Messaging Host 创建成功");
        Ok(())
    }

    /// 记录测试结果
    async fn record_test_result(
        &self,
        test_name: &str,
        result: &Result<(), Box<dyn std::error::Error>>,
        start_time: Instant,
    ) {
        let duration_ms = start_time.elapsed().as_millis() as u64;
        let test_result = match result {
            Ok(_) => TestResult {
                name: test_name.to_string(),
                passed: true,
                duration_ms,
                error_message: None,
                details: HashMap::new(),
            },
            Err(e) => TestResult {
                name: test_name.to_string(),
                passed: false,
                duration_ms,
                error_message: Some(e.to_string()),
                details: HashMap::new(),
            },
        };

        let mut results = self.test_results.write().await;
        results.insert(test_name.to_string(), test_result);
    }

    /// 打印测试摘要
    fn print_test_summary(&self, results: &HashMap<String, TestResult>) {
        let total_tests = results.len();
        let passed_tests = results.values().filter(|r| r.passed).count();
        let failed_tests = total_tests - passed_tests;

        println!("\n📊 测试摘要:");
        println!("总测试数: {}", total_tests);
        println!("通过: {} ✅", passed_tests);
        println!("失败: {} ❌", failed_tests);
        println!("成功率: {:.1}%", (passed_tests as f64 / total_tests as f64) * 100.0);

        if failed_tests > 0 {
            println!("\n❌ 失败的测试:");
            for (name, result) in results {
                if !result.passed {
                    println!("  - {}: {}", name, result.error_message.as_ref().unwrap_or(&"未知错误".to_string()));
                }
            }
        }
    }
}

/// 测试消息处理器
pub struct TestMessageHandler {
    #[allow(dead_code)]
    name: String,
    message_count: Arc<RwLock<u64>>,
    connection_count: Arc<RwLock<u64>>,
}

impl TestMessageHandler {
    pub fn new() -> Self {
        Self {
            name: "test_handler".to_string(),
            message_count: Arc::new(RwLock::new(0)),
            connection_count: Arc::new(RwLock::new(0)),
        }
    }

    pub async fn get_message_count(&self) -> u64 {
        *self.message_count.read().await
    }

    pub async fn get_connection_count(&self) -> u64 {
        *self.connection_count.read().await
    }
}

#[async_trait::async_trait]
impl MessageHandler for TestMessageHandler {
    async fn handle_message(
        &self,
        connection_id: &str,
        message: IpcMessage,
    ) -> Result<Option<IpcResponse>, secure_password_daemon::ipc::IpcError> {
        // 增加消息计数
        {
            let mut count = self.message_count.write().await;
            *count += 1;
        }

        debug!("测试处理器处理消息: {} 来自连接: {}", message.message_id, connection_id);

        match message.message_type {
            IpcMessageType::Ping => {
                Ok(Some(IpcResponse::success(
                    message.message_id,
                    json!({"pong": true, "handler": "test_handler"}),
                )))
            }
            IpcMessageType::BrowserRequest => {
                // 模拟处理浏览器请求
                let action = message.payload.get("action").and_then(|v| v.as_str()).unwrap_or("unknown");

                match action {
                    "get_credentials" => {
                        Ok(Some(IpcResponse::success(
                            message.message_id,
                            json!({
                                "credentials": [
                                    {
                                        "id": 1,
                                        "domain": "example.com",
                                        "username": "testuser",
                                        "password": "encrypted_password"
                                    }
                                ]
                            }),
                        )))
                    }
                    "save_credential" => {
                        Ok(Some(IpcResponse::success(
                            message.message_id,
                            json!({
                                "success": true,
                                "credential_id": 123,
                                "message": "凭证保存成功"
                            }),
                        )))
                    }
                    "update_credential" => {
                        Ok(Some(IpcResponse::success(
                            message.message_id,
                            json!({
                                "success": true,
                                "message": "凭证更新成功"
                            }),
                        )))
                    }
                    "delete_credential" => {
                        Ok(Some(IpcResponse::success(
                            message.message_id,
                            json!({
                                "success": true,
                                "message": "凭证删除成功"
                            }),
                        )))
                    }
                    _ => {
                        Ok(Some(IpcResponse::error(
                            message.message_id,
                            format!("未知操作: {}", action),
                        )))
                    }
                }
            }
            _ => {
                // 其他消息类型不需要响应
                Ok(None)
            }
        }
    }

    async fn handle_connection_event(
        &self,
        event: ServerEvent,
    ) -> Result<(), secure_password_daemon::ipc::IpcError> {
        match event {
            ServerEvent::ClientConnected { connection_id, remote_addr } => {
                {
                    let mut count = self.connection_count.write().await;
                    *count += 1;
                }
                info!("客户端连接: {} 来自: {:?}", connection_id, remote_addr);
            }
            ServerEvent::ClientDisconnected { connection_id, reason } => {
                {
                    let mut count = self.connection_count.write().await;
                    if *count > 0 {
                        *count -= 1;
                    }
                }
                info!("客户端断开: {} 原因: {:?}", connection_id, reason);
            }
            ServerEvent::MessageReceived { connection_id, message } => {
                debug!("收到消息: {} 来自: {}", message.message_id, connection_id);
            }
            ServerEvent::ServerError { error } => {
                error!("连接错误: {:?}", error);
            }
            ServerEvent::MessageSent { connection_id, message } => {
                debug!("发送消息: {:?} 到: {}", message, connection_id);
            }
        }
        Ok(())
    }
}

impl E2EMessageFlowTestSuite {
    /// 测试连接建立
    async fn test_connection_establishment(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let test_name = "connection_establishment";
        let start_time = Instant::now();

        info!("🔗 测试连接建立");

        let result = async {
            // 创建测试客户端
            let client_config = ClientConfig {
                server_address: "127.0.0.1".to_string(),
                port: Some(self.config.ipc_port),
                timeout_ms: 5000,
                request_timeout_ms: 10000,
                reconnect_interval_ms: 1000,
                max_reconnect_attempts: 3,
                heartbeat_interval_ms: 30000,
                auto_reconnect: true,
                transport_type: TransportType::Tcp,
                message_buffer_size: 8192,
            };

            let mut client = IpcClient::new(client_config);

            // 尝试连接到 IPC 服务器
            client.connect().await?;

            // 验证连接状态
            assert_eq!(client.get_state().await, ClientState::Connected);

            // 发送 ping 消息测试连接
            let ping_response = client.ping().await?;
            assert!(ping_response.is_success());

            self.test_client = Some(client);

            info!("连接建立成功");
            Ok::<(), Box<dyn std::error::Error>>(())
        }.await;

        self.record_test_result(test_name, &result, start_time).await;
        result
    }

    /// 测试浏览器到 Tauri 的消息流转
    async fn test_browser_to_tauri_message_flow(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let test_name = "browser_to_tauri_message_flow";
        let start_time = Instant::now();

        info!("🌐 测试浏览器到 Tauri 的消息流转");

        let result = async {
            let client = self.test_client.as_mut().ok_or("测试客户端未初始化")?;

            // 1. 模拟浏览器扩展发送获取凭证请求
            let browser_request = IpcMessage::new(
                IpcMessageType::BrowserRequest,
                json!({
                    "action": "get_credentials",
                    "domain": "example.com",
                    "url": "https://example.com/login"
                }),
                "browser_extension".to_string(),
            );

            // 追踪消息
            let message_tracker = self.message_tracker.clone();
            let tracked_message = TrackedMessage {
                message_id: browser_request.message_id.clone(),
                message_type: format!("{:?}", browser_request.message_type),
                timestamp: Instant::now(),
                source: browser_request.source.clone(),
                target: browser_request.target.clone(),
                payload_summary: format!("{}", browser_request.payload),
            };
            let mut tracker = message_tracker.write().await;
            tracker.sent_messages.push(tracked_message);

            // 发送请求并等待响应
            let response = client.request(browser_request).await?;

            // 追踪响应
            self.track_received_response(&response).await;

            // 验证响应
            assert!(response.is_success());
            assert!(response.data.get("credentials").is_some());

            info!("浏览器到 Tauri 消息流转测试成功");
            Ok::<(), Box<dyn std::error::Error>>(())
        }.await;

        self.record_test_result(test_name, &result, start_time).await;
        result
    }

    /// 测试凭证操作
    async fn test_credential_operations(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let test_name = "credential_operations";
        let start_time = Instant::now();

        info!("🔐 测试凭证操作");

        let result = async {
            let client = self.test_client.as_mut().ok_or("测试客户端未初始化")?;

            // 1. 测试保存凭证
            let save_request = IpcMessage::new(
                IpcMessageType::BrowserRequest,
                json!({
                    "action": "save_credential",
                    "domain": "example.com",
                    "username": "testuser",
                    "password": "testpass123",
                    "website": "example.com",
                    "notes": "Test credential"
                }),
                "browser_extension".to_string(),
            );

            let save_response = client.request(save_request).await?;
            assert!(save_response.is_success());
            assert!(save_response.data.get("credential_id").is_some());

            // 2. 测试更新凭证
            let update_request = IpcMessage::new(
                IpcMessageType::BrowserRequest,
                json!({
                    "action": "update_credential",
                    "id": 123,
                    "domain": "example.com",
                    "new_password": "newpass456"
                }),
                "browser_extension".to_string(),
            );

            let update_response = client.request(update_request).await?;
            assert!(update_response.is_success());

            // 3. 测试删除凭证
            let delete_request = IpcMessage::new(
                IpcMessageType::BrowserRequest,
                json!({
                    "action": "delete_credential",
                    "id": 123,
                    "soft_delete": true
                }),
                "browser_extension".to_string(),
            );

            let delete_response = client.request(delete_request).await?;
            assert!(delete_response.is_success());

            info!("凭证操作测试成功");
            Ok::<(), Box<dyn std::error::Error>>(())
        }.await;

        self.record_test_result(test_name, &result, start_time).await;
        result
    }

    /// 测试错误处理
    async fn test_error_handling(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let test_name = "error_handling";
        let start_time = Instant::now();

        info!("⚠️ 测试错误处理");

        let result = async {
            let client = self.test_client.as_mut().ok_or("测试客户端未初始化")?;

            // 测试未知操作
            let unknown_request = IpcMessage::new(
                IpcMessageType::BrowserRequest,
                json!({
                    "action": "unknown_action",
                    "data": "test"
                }),
                "browser_extension".to_string(),
            );

            let error_response = client.request(unknown_request).await?;
            assert!(!error_response.is_success());
            assert!(error_response.error.is_some());

            info!("错误处理测试成功");
            Ok::<(), Box<dyn std::error::Error>>(())
        }.await;

        self.record_test_result(test_name, &result, start_time).await;
        result
    }

    /// 测试性能指标
    async fn test_performance_metrics(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let test_name = "performance_metrics";
        let start_time = Instant::now();

        info!("📊 测试性能指标");

        let result = async {
            let client = self.test_client.as_mut().ok_or("测试客户端未初始化")?;

            // 发送多个并发请求测试性能
            let mut tasks = Vec::new();

            for i in 0..10 {
                let client_clone = client.clone();
                let task = tokio::spawn(async move {
                    let request = IpcMessage::new(
                        IpcMessageType::BrowserRequest,
                        json!({
                            "action": "get_credentials",
                            "domain": format!("example{}.com", i)
                        }),
                        "browser_extension".to_string(),
                    );

                    client_clone.request(request).await
                });
                tasks.push(task);
            }

            // 等待所有请求完成
            let results = futures::future::join_all(tasks).await;

            // 验证所有请求都成功
            for result in results {
                let response = result??;
                assert!(response.is_success());
            }

            info!("性能指标测试成功");
            Ok::<(), Box<dyn std::error::Error>>(())
        }.await;

        self.record_test_result(test_name, &result, start_time).await;
        result
    }

    /// 测试清理
    async fn test_cleanup(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let test_name = "cleanup";
        let start_time = Instant::now();

        info!("🧹 测试清理");

        let result = async {
            // 断开客户端连接
            if let Some(mut client) = self.test_client.take() {
                client.disconnect().await?;
            }

            // 关闭 IPC 服务器
            if let Some(mut server) = self.ipc_server.take() {
                server.shutdown().await?;
            }

            // 清理测试数据目录
            if tokio::fs::metadata(&self.config.test_data_dir).await.is_ok() {
                tokio::fs::remove_dir_all(&self.config.test_data_dir).await?;
            }

            info!("清理完成");
            Ok::<(), Box<dyn std::error::Error>>(())
        }.await;

        self.record_test_result(test_name, &result, start_time).await;
        result
    }

    /// 追踪接收的响应
    async fn track_received_response(&self, response: &IpcResponse) {
        let tracked_message = TrackedMessage {
            message_id: response.request_id.clone(),
            message_type: format!("{:?}", response.status),
            timestamp: Instant::now(),
            source: response.source.clone(),
            target: None,
            payload_summary: format!("{}", response.data),
        };

        let mut tracker = self.message_tracker.write().await;
        tracker.received_messages.push(tracked_message);
    }
}

/// 创建测试用的守护进程配置
fn create_test_daemon_config() -> DaemonConfig {
    DaemonConfig {
        service: secure_password_daemon::config::ServiceConfig {
            name: "secure-password-test".to_string(),
            display_name: "Secure Password Test Daemon".to_string(),
            description: "Test daemon for secure password manager".to_string(),
            auto_start: false,
            working_directory: Some("/tmp".to_string()),
        },
        ipc: secure_password_daemon::config::IpcConfig {
            transport: secure_password_daemon::config::IpcTransportType::Tcp,
            bind_address: "127.0.0.1".to_string(),
            port: Some(18080),
            max_connections: 10,
            connection_timeout: 30,
        },
        native_messaging: secure_password_daemon::config::NativeMessagingConfig {
            enabled: true,
            host_name: "com.secure.password.test".to_string(),
            supported_browsers: vec!["chrome".to_string(), "firefox".to_string()],
            extension_whitelist: vec!["test-extension".to_string()],
        },
        app_manager: secure_password_daemon::config::AppManagerConfig {
            app_path: "/tmp/test-app".to_string(),
            startup_timeout: 30,
            health_check_interval: 10,
            max_restart_attempts: 3,
        },
        security: secure_password_daemon::config::SecurityConfig {
            enabled: false,
            encryption_algorithm: "AES256".to_string(),
            key_length: 256,
        },
        monitoring: secure_password_daemon::config::MonitoringConfig {
            enabled: false,
            metrics_interval: 60,
            monitoring_port: Some(18081),
        },
        logging: secure_password_daemon::config::LoggingConfig {
            level: "debug".to_string(),
            file_path: None,
            console: true,
            format: secure_password_daemon::config::LogFormat::Text,
        },
    }
}

/// 运行完整的端到端消息流转测试
#[tokio::test]
async fn test_complete_e2e_message_flow() {
    // 初始化日志
    let _ = tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .with_test_writer()
        .try_init();

    println!("🚀 开始运行完整的端到端消息流转测试");

    // 创建测试套件
    let mut test_suite = E2EMessageFlowTestSuite::new().await
        .expect("创建测试套件失败");

    // 运行完整测试
    let results = test_suite.run_complete_message_flow_test().await
        .expect("运行测试套件失败");

    // 验证测试结果
    let total_tests = results.len();
    let passed_tests = results.values().filter(|r| r.passed).count();

    println!("\n📊 测试结果统计:");
    println!("总测试数: {}", total_tests);
    println!("通过: {}", passed_tests);
    println!("失败: {}", total_tests - passed_tests);

    // 确保至少有一些测试通过
    assert!(passed_tests > 0, "至少应有一些测试通过");

    // 如果有失败的测试，打印详细信息
    for (name, result) in &results {
        if !result.passed {
            println!("❌ 测试失败: {} - {}", name,
                result.error_message.as_ref().unwrap_or(&"未知错误".to_string()));
        }
    }

    println!("✅ 端到端消息流转测试完成");
}

/// 测试基本的消息创建和验证
#[tokio::test]
async fn test_basic_message_creation() {
    println!("📨 测试基本消息创建");

    // 测试 IPC 消息创建
    let ipc_message = IpcMessage::new(
        IpcMessageType::BrowserRequest,
        json!({
            "action": "get_credentials",
            "domain": "example.com"
        }),
        "browser_extension".to_string(),
    );

    assert!(!ipc_message.message_id.is_empty());
    assert_eq!(ipc_message.message_type, IpcMessageType::BrowserRequest);
    assert_eq!(ipc_message.source, "browser_extension");

    // 测试 Native Message 创建
    let native_message = NativeMessage::new_request(
        "test_request_001".to_string(),
        json!({
            "action": "get_credentials",
            "domain": "example.com"
        }),
        "browser_extension".to_string(),
    );

    assert_eq!(native_message.request_id, "test_request_001");
    assert_eq!(native_message.source, "browser_extension");

    println!("✅ 基本消息创建测试通过");
}

/// 测试配置创建
#[tokio::test]
async fn test_configuration_creation() {
    println!("🔧 测试配置创建");

    let daemon_config = create_test_daemon_config();
    assert_eq!(daemon_config.service.name, "secure-password-test");
    assert_eq!(daemon_config.ipc.bind_address, "127.0.0.1");
    assert_eq!(daemon_config.ipc.port, Some(18080));

    let e2e_config = E2ETestConfig::default();
    assert_eq!(e2e_config.ipc_port, 18080);
    assert!(!e2e_config.extension_id.is_empty());

    println!("✅ 配置创建测试通过");
}
