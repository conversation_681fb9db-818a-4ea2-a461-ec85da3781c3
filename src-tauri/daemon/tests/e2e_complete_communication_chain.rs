//! 端到端完整通信链路集成测试
//!
//! 测试从浏览器扩展到 Tauri 主应用的完整消息流转

use std::time::Duration;
use std::collections::HashMap;
use serde_json::json;
use tokio::time::timeout;
use uuid::Uuid;

use secure_password_daemon::{
    daemon_core::SecurePasswordDaemon,
    config::DaemonConfig,
    ipc::{
        client::{IpcClient, ClientConfig}, 
        protocol::{IpcMessage, IpcMessageType, MessagePriority},
        transport::TransportType,
    },
    native_messaging::protocol::NativeMessage,
};

/// 基础守护进程启动测试
#[tokio::test]
async fn test_daemon_startup() {
    // 初始化日志
    let _ = tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .with_test_writer()
        .try_init();

    println!("🚀 开始守护进程启动测试");

    // 创建守护进程配置
    let daemon_config = create_test_daemon_config().await;

    // 创建守护进程实例
    let _daemon = SecurePasswordDaemon::new(daemon_config.clone())
        .await
        .expect("创建守护进程失败");

    println!("✅ 守护进程创建成功");

    // 注意：这里不启动守护进程，只测试创建
    // 因为完整启动需要更复杂的环境设置
}

/// 测试配置创建功能
#[tokio::test]
async fn test_config_creation() {
    println!("🔧 开始配置创建测试");

    let daemon_config = create_test_daemon_config().await;

    // 验证配置的基本字段
    assert_eq!(daemon_config.service.name, "secure-password-test");
    assert_eq!(daemon_config.ipc.bind_address, "127.0.0.1");
    assert_eq!(daemon_config.ipc.port, Some(18080));

    println!("✅ 配置创建测试成功！");
}

/// 测试 IPC 客户端创建和基本操作
#[tokio::test]
async fn test_ipc_client_creation() {
    println!("🔗 开始 IPC 客户端创建测试");

    let daemon_config = create_test_daemon_config().await;
    let client_config = create_test_ipc_client_config(&daemon_config);
    
    // 创建客户端
    let client = IpcClient::new(client_config);
    
    // 检查初始状态
    let state = client.get_state().await;
    assert_eq!(state, secure_password_daemon::ipc::client::ClientState::Disconnected);
    
    let stats = client.get_stats().await;
    assert_eq!(stats.messages_sent, 0);
    assert_eq!(stats.messages_received, 0);
    assert_eq!(stats.error_count, 0);

    println!("✅ IPC 客户端创建测试成功！");
}

/// 测试 IPC 消息创建和验证
#[tokio::test]
async fn test_ipc_message_creation() {
    println!("📨 开始 IPC 消息创建测试");

    // 测试基本消息创建
    let message = create_test_ipc_message();
    
    // 验证消息字段
    assert_eq!(message.version, secure_password_daemon::ipc::protocol::IPC_PROTOCOL_VERSION);
    assert!(!message.message_id.is_empty());
    assert_eq!(message.message_type, IpcMessageType::Ping);
    assert_eq!(message.source, "test_client");
    assert_eq!(message.response_required, true);
    assert_eq!(message.priority, MessagePriority::Normal);
    
    // 验证消息格式
    let validation_result = message.validate();
    assert!(validation_result.is_ok(), "消息验证失败: {:?}", validation_result);

    println!("✅ IPC 消息创建测试成功！");
}

/// 测试浏览器消息创建和处理
#[tokio::test]
async fn test_browser_message_creation() {
    println!("🌐 开始浏览器消息创建测试");

    let browser_message = create_test_browser_message();
    
    // 验证浏览器消息字段
    assert_eq!(browser_message.version, 1);
    assert_eq!(browser_message.message_type, "request");
    assert!(!browser_message.request_id.is_empty());
    assert_eq!(browser_message.source, "browser_extension");
    assert_eq!(browser_message.target, Some("secure_password_daemon".to_string()));
    
    // 验证payload
    let payload_action = browser_message.payload["action"].as_str();
    assert_eq!(payload_action, Some("get_credentials"));
    
    let payload_domain = browser_message.payload["domain"].as_str();
    assert_eq!(payload_domain, Some("example.com"));

    println!("✅ 浏览器消息创建测试成功！");
}

/// 测试凭证获取消息流程
#[tokio::test]
async fn test_credential_get_message_flow() {
    println!("🔐 开始凭证获取消息流程测试");

    // 创建凭证获取请求消息
    let get_credentials_message = create_credentials_get_message();
    
    // 验证消息内容
    assert_eq!(get_credentials_message.message_type, IpcMessageType::BrowserRequest);
    assert_eq!(get_credentials_message.source, "browser_extension");
    assert!(get_credentials_message.response_required);
    
    // 验证 payload
    let action = get_credentials_message.payload["action"].as_str();
    assert_eq!(action, Some("get_credentials"));
    
    let domain = get_credentials_message.payload["domain"].as_str();
    assert_eq!(domain, Some("example.com"));

    println!("✅ 凭证获取消息流程测试成功！");
}

/// 测试凭证保存消息流程
#[tokio::test]
async fn test_credential_save_message_flow() {
    println!("💾 开始凭证保存消息流程测试");

    // 创建凭证保存请求消息
    let save_credentials_message = create_credentials_save_message();
    
    // 验证消息内容
    assert_eq!(save_credentials_message.message_type, IpcMessageType::BrowserRequest);
    assert_eq!(save_credentials_message.source, "browser_extension");
    assert!(save_credentials_message.response_required);
    
    // 验证 payload
    let action = save_credentials_message.payload["action"].as_str();
    assert_eq!(action, Some("save_credential"));
    
    let username = save_credentials_message.payload["username"].as_str();
    assert_eq!(username, Some("testuser"));
    
    let password = save_credentials_message.payload["password"].as_str();
    assert_eq!(password, Some("testpass123"));

    println!("✅ 凭证保存消息流程测试成功！");
}

/// 测试凭证更新消息流程
#[tokio::test]
async fn test_credential_update_message_flow() {
    println!("🔄 开始凭证更新消息流程测试");

    // 创建凭证更新请求消息
    let update_credentials_message = create_credentials_update_message();
    
    // 验证消息内容
    assert_eq!(update_credentials_message.message_type, IpcMessageType::BrowserRequest);
    assert_eq!(update_credentials_message.source, "browser_extension");
    assert!(update_credentials_message.response_required);
    
    // 验证 payload
    let action = update_credentials_message.payload["action"].as_str();
    assert_eq!(action, Some("update_credential"));
    
    let id = update_credentials_message.payload["id"].as_u64();
    assert_eq!(id, Some(123));
    
    let new_password = update_credentials_message.payload["new_password"].as_str();
    assert_eq!(new_password, Some("newpass456"));

    println!("✅ 凭证更新消息流程测试成功！");
}

/// 测试凭证删除消息流程
#[tokio::test]
async fn test_credential_delete_message_flow() {
    println!("🗑️ 开始凭证删除消息流程测试");

    // 创建凭证删除请求消息
    let delete_credentials_message = create_credentials_delete_message();
    
    // 验证消息内容
    assert_eq!(delete_credentials_message.message_type, IpcMessageType::BrowserRequest);
    assert_eq!(delete_credentials_message.source, "browser_extension");
    assert!(delete_credentials_message.response_required);
    
    // 验证 payload
    let action = delete_credentials_message.payload["action"].as_str();
    assert_eq!(action, Some("delete_credential"));
    
    let id = delete_credentials_message.payload["id"].as_u64();
    assert_eq!(id, Some(123));
    
    let soft_delete = delete_credentials_message.payload["soft_delete"].as_bool();
    assert_eq!(soft_delete, Some(true));

    println!("✅ 凭证删除消息流程测试成功！");
}

/// 测试错误处理和超时机制
#[tokio::test]
async fn test_error_handling_and_timeout() {
    println!("⚠️ 开始错误处理和超时机制测试");

    let daemon_config = create_test_daemon_config().await;
    let client_config = create_test_ipc_client_config(&daemon_config);
    
    let mut client = IpcClient::new(client_config);
    
    // 测试连接到不存在的服务器（应该超时或失败）
    let connect_result = timeout(
        Duration::from_millis(2000), // 2秒超时
        client.connect()
    ).await;
    
    // 连接应该失败或超时
    match connect_result {
        Ok(Ok(_)) => {
            // 如果连接成功，说明有服务器在运行，这在测试环境中是可能的
            println!("⚠️ 连接意外成功 - 可能有服务器在运行");
        }
        Ok(Err(e)) => {
            println!("✅ 连接正确失败: {:?}", e);
        }
        Err(_) => {
            println!("✅ 连接正确超时");
        }
    }
    
    // 测试在未连接状态下发送请求
    let ping_result = client.ping().await;
    assert!(ping_result.is_err(), "在未连接状态下 ping 应该失败");

    println!("✅ 错误处理和超时机制测试成功！");
}

/// 测试消息序列化和反序列化
#[tokio::test]
async fn test_message_serialization() {
    println!("📦 开始消息序列化测试");

    // 测试 IPC 消息序列化
    let original_message = create_test_ipc_message();
    
    // 序列化
    let serialized = serde_json::to_vec(&original_message)
        .expect("IPC 消息序列化失败");
    
    // 反序列化
    let deserialized: IpcMessage = serde_json::from_slice(&serialized)
        .expect("IPC 消息反序列化失败");
    
    // 验证数据一致性
    assert_eq!(original_message.message_id, deserialized.message_id);
    assert_eq!(original_message.message_type, deserialized.message_type);
    assert_eq!(original_message.source, deserialized.source);
    assert_eq!(original_message.priority, deserialized.priority);
    
    // 测试浏览器消息序列化
    let original_browser_message = create_test_browser_message();
    
    // 序列化
    let browser_serialized = serde_json::to_vec(&original_browser_message)
        .expect("浏览器消息序列化失败");
    
    // 反序列化
    let browser_deserialized: NativeMessage = serde_json::from_slice(&browser_serialized)
        .expect("浏览器消息反序列化失败");
    
    // 验证数据一致性
    assert_eq!(original_browser_message.request_id, browser_deserialized.request_id);
    assert_eq!(original_browser_message.message_type, browser_deserialized.message_type);
    assert_eq!(original_browser_message.source, browser_deserialized.source);

    println!("✅ 消息序列化测试成功！");
}

/// 创建测试用的守护进程配置
async fn create_test_daemon_config() -> DaemonConfig {
    DaemonConfig {
        service: secure_password_daemon::config::ServiceConfig {
            name: "secure-password-test".to_string(),
            display_name: "Secure Password Test Daemon".to_string(),
            description: "Test daemon for secure password manager".to_string(),
            auto_start: false,
            working_directory: Some("/tmp".to_string()),
        },
        ipc: secure_password_daemon::config::IpcConfig {
            transport: secure_password_daemon::config::IpcTransportType::Tcp,
            bind_address: "127.0.0.1".to_string(),
            port: Some(18080), // 使用测试端口
            max_connections: 10,
            connection_timeout: 30,
        },
        native_messaging: secure_password_daemon::config::NativeMessagingConfig {
            enabled: true,
            host_name: "com.secure.password.test".to_string(),
            supported_browsers: vec!["chrome".to_string(), "firefox".to_string()],
            extension_whitelist: vec!["test-extension".to_string()],
        },
        app_manager: secure_password_daemon::config::AppManagerConfig {
            app_path: "/tmp/test-app".to_string(),
            startup_timeout: 30,
            health_check_interval: 10,
            max_restart_attempts: 3,
        },
        security: secure_password_daemon::config::SecurityConfig {
            enabled: false, // 测试时禁用安全检查
            encryption_algorithm: "AES256".to_string(),
            key_length: 256,
        },
        monitoring: secure_password_daemon::config::MonitoringConfig {
            enabled: false, // 测试时禁用监控
            metrics_interval: 60,
            monitoring_port: Some(18081),
        },
        logging: secure_password_daemon::config::LoggingConfig {
            level: "info".to_string(),
            file_path: None,
            console: true,
            format: secure_password_daemon::config::LogFormat::Text,
        },
    }
}

/// 创建测试用的 IPC 客户端配置
fn create_test_ipc_client_config(daemon_config: &DaemonConfig) -> ClientConfig {
    ClientConfig {
        server_address: daemon_config.ipc.bind_address.clone(),
        port: daemon_config.ipc.port,
        timeout_ms: 5000,
        request_timeout_ms: 10000,
        reconnect_interval_ms: 1000,
        max_reconnect_attempts: 3,
        heartbeat_interval_ms: 30000,
        auto_reconnect: true,
        transport_type: TransportType::Tcp,
        message_buffer_size: 8192,
    }
}

/// 创建测试用的浏览器消息
fn create_test_browser_message() -> NativeMessage {
    NativeMessage {
        version: 1,
        message_type: "request".to_string(),
        request_id: Uuid::new_v4().to_string(),
        payload: json!({
            "action": "get_credentials",
            "domain": "example.com",
            "url": "https://example.com/login"
        }),
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        source: "browser_extension".to_string(),
        target: Some("secure_password_daemon".to_string()),
        signature: None,
        extensions: HashMap::new(),
    }
}

/// 创建测试用的 IPC 消息
fn create_test_ipc_message() -> IpcMessage {
    IpcMessage {
        version: secure_password_daemon::ipc::protocol::IPC_PROTOCOL_VERSION,
        message_id: Uuid::new_v4().to_string(),
        message_type: IpcMessageType::Ping,
        payload: json!({"test": "ping"}),
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        source: "test_client".to_string(),
        target: Some("daemon".to_string()),
        response_required: true,
        priority: MessagePriority::Normal,
        headers: HashMap::new(),
    }
}

/// 创建凭证获取请求消息
fn create_credentials_get_message() -> IpcMessage {
    IpcMessage {
        version: secure_password_daemon::ipc::protocol::IPC_PROTOCOL_VERSION,
        message_id: Uuid::new_v4().to_string(),
        message_type: IpcMessageType::BrowserRequest,
        payload: json!({
            "action": "get_credentials",
            "domain": "example.com",
            "url": "https://example.com/login"
        }),
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        source: "browser_extension".to_string(),
        target: Some("daemon".to_string()),
        response_required: true,
        priority: MessagePriority::Normal,
        headers: HashMap::new(),
    }
}

/// 创建凭证保存请求消息
fn create_credentials_save_message() -> IpcMessage {
    IpcMessage {
        version: secure_password_daemon::ipc::protocol::IPC_PROTOCOL_VERSION,
        message_id: Uuid::new_v4().to_string(),
        message_type: IpcMessageType::BrowserRequest,
        payload: json!({
            "action": "save_credential",
            "domain": "example.com",
            "url": "https://example.com/login",
            "username": "testuser",
            "password": "testpass123",
            "website": "example.com",
            "notes": "Test credential"
        }),
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        source: "browser_extension".to_string(),
        target: Some("daemon".to_string()),
        response_required: true,
        priority: MessagePriority::Normal,
        headers: HashMap::new(),
    }
}

/// 创建凭证更新请求消息
fn create_credentials_update_message() -> IpcMessage {
    IpcMessage {
        version: secure_password_daemon::ipc::protocol::IPC_PROTOCOL_VERSION,
        message_id: Uuid::new_v4().to_string(),
        message_type: IpcMessageType::BrowserRequest,
        payload: json!({
            "action": "update_credential",
            "id": 123,
            "domain": "example.com",
            "new_password": "newpass456",
            "notes": "Updated test credential"
        }),
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        source: "browser_extension".to_string(),
        target: Some("daemon".to_string()),
        response_required: true,
        priority: MessagePriority::Normal,
        headers: HashMap::new(),
    }
}

/// 创建凭证删除请求消息
fn create_credentials_delete_message() -> IpcMessage {
    IpcMessage {
        version: secure_password_daemon::ipc::protocol::IPC_PROTOCOL_VERSION,
        message_id: Uuid::new_v4().to_string(),
        message_type: IpcMessageType::BrowserRequest,
        payload: json!({
            "action": "delete_credential",
            "id": 123,
            "soft_delete": true
        }),
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        source: "browser_extension".to_string(),
        target: Some("daemon".to_string()),
        response_required: true,
        priority: MessagePriority::Normal,
        headers: HashMap::new(),
    }
}
