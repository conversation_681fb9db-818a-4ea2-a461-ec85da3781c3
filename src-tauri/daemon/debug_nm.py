#!/usr/bin/env python3
"""
调试Native Messaging Host的字节流问题
"""

import json
import struct
import subprocess
import sys
import time
import os

def create_test_message():
    """创建测试消息"""
    message = {
        "version": 2,
        "message_type": "ping",
        "request_id": "test-001",
        "timestamp": int(time.time()),
        "source": "test-extension",
        "payload": {}
    }
    
    message_json = json.dumps(message)
    message_bytes = message_json.encode('utf-8')
    length = len(message_bytes)
    length_bytes = struct.pack('<I', length)
    
    print(f"🔍 调试信息:")
    print(f"  消息内容: {message_json}")
    print(f"  消息长度: {length} 字节")
    print(f"  长度字节 (小端): {length_bytes.hex()}")
    print(f"  消息字节前20个: {message_bytes[:20].hex()}")
    
    return length_bytes + message_bytes

def debug_daemon_output():
    """调试守护进程输出"""
    daemon_path = "/Users/<USER>/Documents/tarui/secure-password/src-tauri/target/release/secure-password-daemon"
    
    if not os.path.exists(daemon_path):
        print(f"❌ 守护进程文件不存在: {daemon_path}")
        return False
    
    print("🚀 启动Native Messaging Host (调试模式)...")
    
    try:
        # 启动守护进程，捕获所有输出
        process = subprocess.Popen(
            [daemon_path, "--native-messaging", "--log-level", "debug"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd="/Users/<USER>/Documents/tarui/secure-password/src-tauri/daemon"
        )
        
        print("⏳ 等待进程启动...")
        time.sleep(3)  # 给进程更多时间启动
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ 进程已退出，返回码: {process.returncode}")
            print(f"📤 标准输出: {stdout.decode('utf-8', errors='ignore')}")
            print(f"📤 标准错误: {stderr.decode('utf-8', errors='ignore')}")
            return False
        
        print("✅ 进程启动成功，准备发送测试消息...")
        
        # 创建测试消息
        test_message = create_test_message()
        
        print(f"📤 发送完整消息 ({len(test_message)} 字节): {test_message.hex()}")
        
        # 发送消息
        process.stdin.write(test_message)
        process.stdin.flush()
        
        print("⏳ 等待响应...")
        
        # 尝试读取响应
        try:
            # 设置读取超时
            import select
            ready, _, _ = select.select([process.stdout], [], [], 10.0)
            if ready:
                # 读取前8个字节来调试
                first_bytes = process.stdout.read(8)
                print(f"📥 接收到前8字节: {first_bytes.hex()}")
                
                if len(first_bytes) >= 4:
                    # 尝试解析长度
                    length = struct.unpack('<I', first_bytes[:4])[0]
                    print(f"📥 解析出的长度: {length}")
                    
                    if length < 1024 * 1024:  # 合理的长度
                        # 读取剩余数据
                        remaining = length - len(first_bytes) + 4
                        if remaining > 0:
                            rest_data = process.stdout.read(remaining)
                            full_response = first_bytes + rest_data
                            print(f"📥 完整响应 ({len(full_response)} 字节): {full_response.hex()}")
                            
                            # 尝试解析JSON
                            if len(full_response) >= 4 + length:
                                json_data = full_response[4:4+length]
                                try:
                                    response = json.loads(json_data.decode('utf-8'))
                                    print(f"✅ 成功解析响应: {json.dumps(response, indent=2)}")
                                    return True
                                except json.JSONDecodeError as e:
                                    print(f"❌ JSON解析失败: {e}")
                                    print(f"原始JSON数据: {json_data}")
                    else:
                        print(f"❌ 长度过大: {length}")
                else:
                    print("❌ 接收的数据不足4字节")
            else:
                print("⏰ 读取响应超时")
        except Exception as e:
            print(f"❌ 读取响应时出错: {e}")
        
        # 获取stderr输出来查看日志
        try:
            process.stdin.close()
            _, stderr = process.communicate(timeout=5)
            if stderr:
                print(f"📋 守护进程日志:")
                print(stderr.decode('utf-8', errors='ignore'))
        except subprocess.TimeoutExpired:
            process.kill()
            
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def main():
    print("=" * 60)
    print("Native Messaging Host 字节流调试")
    print("=" * 60)
    
    success = debug_daemon_output()
    
    print("=" * 60)
    if success:
        print("🎉 调试成功!")
    else:
        print("💥 调试发现问题!")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main()) 