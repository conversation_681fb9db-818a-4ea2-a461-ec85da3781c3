#!/usr/bin/env python3
"""
端到端Native Messaging测试
"""

import json
import struct
import subprocess
import sys
import time
import threading
import signal
import os

class NativeMessagingTester:
    def __init__(self):
        self.daemon_process = None
        self.daemon_ready = False
        
    def start_daemon(self):
        """启动完整的守护进程"""
        daemon_path = "/Users/<USER>/Documents/tarui/secure-password/src-tauri/target/release/secure-password-daemon"
        
        print("🚀 启动完整守护进程...")
        self.daemon_process = subprocess.Popen(
            [daemon_path, "--daemon"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待守护进程启动
        time.sleep(3)
        
        if self.daemon_process.poll() is None:
            print("✅ 守护进程启动成功")
            self.daemon_ready = True
            return True
        else:
            print("❌ 守护进程启动失败")
            return False
    
    def test_native_messaging_host(self):
        """测试Native Messaging Host"""
        if not self.daemon_ready:
            print("❌ 守护进程未就绪")
            return False
            
        daemon_path = "/Users/<USER>/Documents/tarui/secure-password/src-tauri/target/release/secure-password-daemon"
        
        print("🔗 启动Native Messaging Host...")
        nm_process = subprocess.Popen(
            [daemon_path, "--native-messaging"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        try:
            # 发送ping消息
            ping_message = {
                "version": "2.0",
                "message_type": "ping",
                "request_id": "test-001",
                "timestamp": int(time.time()),
                "source": "test-extension",
                "payload": {},
                "extensions": {
                    "extension_id": "test-extension-id"
                }
            }
            
            print(f"📤 发送消息: {json.dumps(ping_message, indent=2)}")
            
            # 发送消息
            self.send_message(nm_process, ping_message)
            
            # 读取响应
            response = self.read_message(nm_process)
            
            if response:
                print(f"📥 接收响应: {json.dumps(response, indent=2)}")
                
                if response.get("message_type") == "pong":
                    print("✅ Native Messaging Host测试成功!")
                    return True
                else:
                    print("❌ 响应类型不正确")
                    return False
            else:
                print("❌ 未收到响应")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            return False
        finally:
            nm_process.terminate()
    
    def test_auto_detection(self):
        """测试自动检测功能"""
        daemon_path = "/Users/<USER>/Documents/tarui/secure-password/src-tauri/target/release/secure-password-daemon"
        
        print("🔍 测试自动检测功能...")
        
        # 创建测试消息
        ping_message = {
            "version": "2.0",
            "message_type": "ping",
            "request_id": "auto-test-001",
            "timestamp": int(time.time()),
            "source": "test-extension",
            "payload": {},
            "extensions": {
                "extension_id": "test-extension-id"
            }
        }
        
        # 直接启动守护进程（不带参数），通过stdin发送消息
        process = subprocess.Popen(
            [daemon_path],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        try:
            print(f"📤 发送消息: {json.dumps(ping_message, indent=2)}")
            
            # 发送消息
            self.send_message(process, ping_message)
            
            # 读取响应
            response = self.read_message(process)
            
            if response:
                print(f"📥 接收响应: {json.dumps(response, indent=2)}")
                
                if response.get("message_type") == "pong":
                    print("✅ 自动检测功能测试成功!")
                    return True
                else:
                    print("❌ 响应类型不正确")
                    return False
            else:
                print("❌ 未收到响应")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            return False
        finally:
            process.terminate()
    
    def send_message(self, process, message):
        """发送Native Messaging消息"""
        message_json = json.dumps(message)
        message_bytes = message_json.encode('utf-8')
        length = len(message_bytes)
        length_bytes = struct.pack('<I', length)
        
        process.stdin.write(length_bytes)
        process.stdin.write(message_bytes)
        process.stdin.flush()
    
    def read_message(self, process):
        """读取Native Messaging消息"""
        try:
            # 设置超时
            import select
            ready, _, _ = select.select([process.stdout], [], [], 5.0)
            if not ready:
                print("⏰ 读取消息超时")
                return None
            
            length_bytes = process.stdout.read(4)
            if len(length_bytes) != 4:
                print("❌ 无法读取消息长度")
                return None
            
            length = struct.unpack('<I', length_bytes)[0]
            if length > 1024 * 1024:  # 1MB限制
                print(f"❌ 消息过大: {length} bytes")
                return None
                
            message_bytes = process.stdout.read(length)
            if len(message_bytes) != length:
                print("❌ 消息读取不完整")
                return None
            
            try:
                message = json.loads(message_bytes.decode('utf-8'))
                return message
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return None
                
        except Exception as e:
            print(f"❌ 读取消息时出错: {e}")
            return None
    
    def cleanup(self):
        """清理资源"""
        if self.daemon_process and self.daemon_process.poll() is None:
            print("🧹 清理守护进程...")
            self.daemon_process.terminate()
            self.daemon_process.wait()

def main():
    tester = NativeMessagingTester()
    
    try:
        print("=" * 60)
        print("🧪 Native Messaging 端到端测试")
        print("=" * 60)
        
        # 测试1: 自动检测功能
        print("\n📋 测试1: 自动检测功能")
        print("-" * 40)
        auto_detection_success = tester.test_auto_detection()
        
        # 测试2: 完整的守护进程 + Native Messaging Host
        print("\n📋 测试2: 完整守护进程模式")
        print("-" * 40)
        if tester.start_daemon():
            time.sleep(2)  # 等待IPC服务器启动
            nm_success = tester.test_native_messaging_host()
        else:
            nm_success = False
        
        # 总结
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        print(f"自动检测功能: {'✅ 通过' if auto_detection_success else '❌ 失败'}")
        print(f"Native Messaging Host: {'✅ 通过' if nm_success else '❌ 失败'}")
        
        if auto_detection_success and nm_success:
            print("\n🎉 所有测试通过！Native Messaging Host工作正常")
            return 0
        else:
            print("\n💥 部分测试失败，需要进一步调试")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
        return 1
    finally:
        tester.cleanup()

if __name__ == "__main__":
    sys.exit(main())
