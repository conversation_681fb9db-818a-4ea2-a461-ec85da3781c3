#!/usr/bin/env python3
"""
调试Native Messaging协议
"""

import json
import struct
import subprocess
import sys

def create_test_message():
    """创建测试消息"""
    return {
        "version": "2.0",
        "message_type": "ping",
        "request_id": "debug-001",
        "timestamp": 1752049000,
        "source": "test-extension",
        "payload": {},
        "extensions": {
            "extension_id": "test-extension-id"
        }
    }

def encode_message(message):
    """编码Native Messaging消息"""
    message_json = json.dumps(message)
    message_bytes = message_json.encode('utf-8')
    length = len(message_bytes)
    length_bytes = struct.pack('<I', length)
    
    print(f"消息JSON: {message_json}")
    print(f"消息字节长度: {length}")
    print(f"长度前缀: {length_bytes.hex()}")
    print(f"消息字节: {message_bytes[:50]}...")
    
    return length_bytes + message_bytes

def decode_message_length(data):
    """解码消息长度"""
    if len(data) < 4:
        return None
    
    length_bytes = data[:4]
    length = struct.unpack('<I', length_bytes)[0]
    
    print(f"接收到的长度字节: {length_bytes.hex()}")
    print(f"解析出的长度: {length}")
    
    return length

def test_encoding():
    """测试编码"""
    print("=" * 50)
    print("测试消息编码")
    print("=" * 50)
    
    message = create_test_message()
    encoded = encode_message(message)
    
    print(f"完整编码消息长度: {len(encoded)}")
    print(f"完整编码消息前20字节: {encoded[:20].hex()}")
    
    # 验证解码
    length = decode_message_length(encoded)
    if length:
        message_part = encoded[4:4+length]
        try:
            decoded = json.loads(message_part.decode('utf-8'))
            print(f"解码成功: {decoded['message_type']}")
        except Exception as e:
            print(f"解码失败: {e}")

def test_with_daemon():
    """使用守护进程测试"""
    print("\n" + "=" * 50)
    print("使用守护进程测试")
    print("=" * 50)
    
    daemon_path = "/Users/<USER>/Documents/tarui/secure-password/src-tauri/target/release/secure-password-daemon"
    
    # 创建测试消息
    message = create_test_message()
    encoded = encode_message(message)
    
    print(f"启动守护进程: {daemon_path}")
    
    # 启动守护进程
    process = subprocess.Popen(
        [daemon_path],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    try:
        print("发送编码消息...")
        process.stdin.write(encoded)
        process.stdin.flush()
        
        print("等待响应...")
        
        # 尝试读取响应长度
        response_length_bytes = process.stdout.read(4)
        if len(response_length_bytes) == 4:
            response_length = struct.unpack('<I', response_length_bytes)[0]
            print(f"响应长度: {response_length}")
            
            if response_length > 0 and response_length < 1024 * 1024:
                response_bytes = process.stdout.read(response_length)
                if len(response_bytes) == response_length:
                    try:
                        response = json.loads(response_bytes.decode('utf-8'))
                        print(f"响应: {json.dumps(response, indent=2)}")
                    except Exception as e:
                        print(f"响应解析失败: {e}")
                        print(f"原始响应: {response_bytes}")
                else:
                    print(f"响应读取不完整: 期望{response_length}, 实际{len(response_bytes)}")
            else:
                print(f"响应长度异常: {response_length}")
        else:
            print(f"无法读取响应长度: {len(response_length_bytes)}")
            
        # 检查stderr
        stderr_data = process.stderr.read()
        if stderr_data:
            print(f"错误输出: {stderr_data.decode('utf-8', errors='ignore')}")
            
    except Exception as e:
        print(f"测试过程中出错: {e}")
    finally:
        process.terminate()

def main():
    print("🔍 Native Messaging 协议调试")
    
    # 测试编码
    test_encoding()
    
    # 测试与守护进程的通信
    test_with_daemon()

if __name__ == "__main__":
    main()
