#!/usr/bin/env python3
"""
测试Native Messaging Host的stdin/stdout通信
模拟浏览器扩展与Native Messaging Host的通信
"""

import json
import struct
import subprocess
import sys
import os

def send_message(process, message):
    """向Native Messaging Host发送消息"""
    # 将消息转换为JSON字符串
    message_json = json.dumps(message)
    message_bytes = message_json.encode('utf-8')
    
    # 发送长度前缀（4字节，小端序）
    length = len(message_bytes)
    length_bytes = struct.pack('<I', length)
    
    print(f"发送消息长度: {length}")
    print(f"发送消息内容: {message_json}")
    
    # 发送长度和消息
    process.stdin.write(length_bytes)
    process.stdin.write(message_bytes)
    process.stdin.flush()

def read_message(process):
    """从Native Messaging Host读取消息"""
    # 读取长度前缀（4字节）
    length_bytes = process.stdout.read(4)
    if len(length_bytes) != 4:
        print(f"错误: 无法读取长度前缀，只读取到 {len(length_bytes)} 字节")
        return None
    
    # 解析长度
    length = struct.unpack('<I', length_bytes)[0]
    print(f"接收消息长度: {length}")
    
    # 读取消息内容
    message_bytes = process.stdout.read(length)
    if len(message_bytes) != length:
        print(f"错误: 期望读取 {length} 字节，实际读取 {len(message_bytes)} 字节")
        return None
    
    # 解析JSON
    try:
        message = json.loads(message_bytes.decode('utf-8'))
        print(f"接收消息内容: {json.dumps(message, indent=2)}")
        return message
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败: {e}")
        print(f"原始数据: {message_bytes}")
        return None

def test_native_messaging_host():
    """测试Native Messaging Host"""
    daemon_path = "/Users/<USER>/Documents/tarui/secure-password/src-tauri/target/release/secure-password-daemon"
    
    if not os.path.exists(daemon_path):
        print(f"错误: 守护进程文件不存在: {daemon_path}")
        return False
    
    print("启动Native Messaging Host...")
    
    try:
        # 启动守护进程，使用Native Messaging模式
        process = subprocess.Popen(
            [daemon_path, "--native-messaging"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd="/Users/<USER>/Documents/tarui/secure-password/src-tauri/daemon"
        )
        
        print("等待进程启动...")
        import time
        time.sleep(2)  # 给进程一些时间启动
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"进程已退出，返回码: {process.returncode}")
            print(f"标准输出: {stdout.decode('utf-8')}")
            print(f"标准错误: {stderr.decode('utf-8')}")
            return False
        
        print("发送心跳消息...")
        
        # 发送心跳消息
        ping_message = {
            "version": "2.0",
            "message_type": "ping",
            "request_id": "test-ping-001",
            "timestamp": 1625097600,
            "source": "test-extension",
            "payload": {},
            "extensions": {
                "extension_id": "test-extension-id"
            }
        }
        
        send_message(process, ping_message)
        
        print("等待响应...")
        response = read_message(process)
        
        if response:
            print("✅ 成功接收到响应!")
            if response.get("message_type") == "pong":
                print("✅ 心跳测试成功!")
                return True
            else:
                print(f"❌ 期望收到pong消息，实际收到: {response.get('message_type')}")
        else:
            print("❌ 未收到响应")
        
        return False
        
    except Exception as e:
        print(f"错误: {e}")
        return False
    finally:
        if 'process' in locals():
            process.terminate()
            process.wait()

def test_with_timeout():
    """带超时的测试"""
    import signal
    
    def timeout_handler(signum, frame):
        print("❌ 测试超时")
        sys.exit(1)
    
    # 设置30秒超时
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(30)
    
    try:
        result = test_native_messaging_host()
        signal.alarm(0)  # 取消超时
        return result
    except Exception as e:
        signal.alarm(0)
        print(f"测试异常: {e}")
        return False

if __name__ == "__main__":
    print("=== Native Messaging Host 测试 ===")
    success = test_with_timeout()
    
    if success:
        print("\n🎉 测试成功!")
        sys.exit(0)
    else:
        print("\n❌ 测试失败!")
        sys.exit(1)
