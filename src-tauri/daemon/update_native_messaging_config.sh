#!/bin/bash

# 更新Chrome Native Messaging配置文件

CONFIG_DIR="/Users/<USER>/Library/Application Support/Google/Chrome/NativeMessagingHosts"
CONFIG_FILE="$CONFIG_DIR/com.secure_password.native_messaging_host.json"
DAEMON_PATH="/Users/<USER>/Documents/tarui/secure-password/src-tauri/target/release/secure-password-daemon"

# 确保目录存在
mkdir -p "$CONFIG_DIR"

# 创建配置文件
cat > "$CONFIG_FILE" << EOF
{
  "name": "com.secure_password.native_messaging_host",
  "description": "Native messaging host for Secure Password Tauri App",
  "path": "$DAEMON_PATH",
  "type": "stdio",
  "allowed_origins": [
    "chrome-extension://laiidmaciomhdbdinfaegennlkbdecjp/"
  ]
}
EOF

echo "✅ Chrome Native Messaging配置文件已更新: $CONFIG_FILE"
echo "配置内容:"
cat "$CONFIG_FILE"
