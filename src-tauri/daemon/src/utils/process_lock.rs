//! 进程锁定模块
//! 
//! 提供进程级别的互斥锁，确保只有一个守护进程实例在运行

use std::fs::{File, OpenOptions};
use std::io::{self, Write, Read};
use std::path::{Path, PathBuf};
use tracing::{info, warn, error, debug};

#[cfg(unix)]
use std::os::unix::io::AsRawFd;

/// 进程锁定器
pub struct ProcessLock {
    /// 锁文件路径
    lock_file_path: PathBuf,
    /// 锁文件句柄
    lock_file: Option<File>,
    /// 当前进程ID
    process_id: u32,
}

impl ProcessLock {
    /// 创建新的进程锁定器
    pub fn new(lock_name: &str) -> Self {
        let lock_file_path = Self::get_lock_file_path(lock_name);
        let process_id = std::process::id();
        
        Self {
            lock_file_path,
            lock_file: None,
            process_id,
        }
    }
    
    /// 尝试获取进程锁
    pub fn try_lock(&mut self) -> Result<bool, io::Error> {
        debug!("尝试获取进程锁: {:?}", self.lock_file_path);
        
        // 检查是否已有锁文件存在
        if self.is_already_locked()? {
            info!("检测到已存在的锁文件，检查进程是否仍在运行");
            return Ok(false);
        }
        
        // 创建锁文件
        let mut file = OpenOptions::new()
            .create(true)
            .write(true)
            .truncate(true)
            .open(&self.lock_file_path)?;
        
        // 在Unix系统上使用文件锁
        #[cfg(unix)]
        {
            if !self.acquire_file_lock(&file)? {
                warn!("无法获取文件锁，可能有其他进程正在运行");
                return Ok(false);
            }
        }
        
        // 写入当前进程ID
        write!(file, "{}", self.process_id)?;
        file.flush()?;
        
        self.lock_file = Some(file);
        info!("成功获取进程锁，PID: {}", self.process_id);
        Ok(true)
    }
    
    /// 释放进程锁
    pub fn release(&mut self) -> Result<(), io::Error> {
        debug!("释放进程锁");
        
        if let Some(_file) = self.lock_file.take() {
            // 文件会在drop时自动关闭，Unix文件锁也会自动释放
        }
        
        // 删除锁文件
        if self.lock_file_path.exists() {
            std::fs::remove_file(&self.lock_file_path)?;
            info!("锁文件已删除: {:?}", self.lock_file_path);
        }
        
        Ok(())
    }
    
    /// 检查是否已有锁存在
    fn is_already_locked(&self) -> Result<bool, io::Error> {
        if !self.lock_file_path.exists() {
            return Ok(false);
        }
        
        // 读取锁文件中的PID
        let mut file = File::open(&self.lock_file_path)?;
        let mut content = String::new();
        file.read_to_string(&mut content)?;
        
        if let Ok(pid) = content.trim().parse::<u32>() {
            // 检查该PID的进程是否仍在运行
            if self.is_process_running(pid) {
                warn!("检测到运行中的守护进程实例，PID: {}", pid);
                return Ok(true);
            } else {
                info!("检测到陈旧的锁文件，对应进程已不存在，PID: {}", pid);
                // 删除陈旧的锁文件
                let _ = std::fs::remove_file(&self.lock_file_path);
                return Ok(false);
            }
        }
        
        warn!("锁文件内容无效，删除并继续");
        let _ = std::fs::remove_file(&self.lock_file_path);
        Ok(false)
    }
    
    /// 检查指定PID的进程是否在运行
    fn is_process_running(&self, pid: u32) -> bool {
        #[cfg(unix)]
        {
            // 使用kill(pid, 0)检查进程是否存在
            unsafe {
                libc::kill(pid as libc::pid_t, 0) == 0
            }
        }
        
        #[cfg(windows)]
        {
            use winapi::um::processthreadsapi::OpenProcess;
            use winapi::um::winnt::PROCESS_QUERY_INFORMATION;
            use winapi::um::handleapi::CloseHandle;
            
            unsafe {
                let handle = OpenProcess(PROCESS_QUERY_INFORMATION, 0, pid);
                if handle.is_null() {
                    false
                } else {
                    CloseHandle(handle);
                    true
                }
            }
        }
        
        #[cfg(not(any(unix, windows)))]
        {
            // 其他平台的fallback实现
            false
        }
    }
    
    /// 获取锁文件路径
    fn get_lock_file_path(lock_name: &str) -> PathBuf {
        #[cfg(unix)]
        {
            // Unix系统使用/var/run或/tmp
            let lock_dir = if Path::new("/var/run").exists() && Path::new("/var/run").is_dir() {
                PathBuf::from("/var/run")
            } else {
                PathBuf::from("/tmp")
            };
            lock_dir.join(format!("{}.pid", lock_name))
        }
        
        #[cfg(windows)]
        {
            // Windows系统使用临时目录
            let temp_dir = std::env::temp_dir();
            temp_dir.join(format!("{}.pid", lock_name))
        }
        
        #[cfg(not(any(unix, windows)))]
        {
            // 其他平台使用当前目录
            PathBuf::from(format!("{}.pid", lock_name))
        }
    }
    
    /// 在Unix系统上获取文件锁
    #[cfg(unix)]
    fn acquire_file_lock(&self, file: &File) -> Result<bool, io::Error> {
        use std::mem;
        
        let fd = file.as_raw_fd();
        let mut flock = libc::flock {
            l_type: libc::F_WRLCK as libc::c_short,
            l_whence: libc::SEEK_SET as libc::c_short,
            l_start: 0,
            l_len: 0,
            l_pid: 0,
        };
        
        let result = unsafe {
            libc::fcntl(fd, libc::F_SETLK, &mut flock as *mut libc::flock)
        };
        
        if result == -1 {
            let error = io::Error::last_os_error();
            if error.kind() == io::ErrorKind::WouldBlock {
                Ok(false) // 锁被其他进程持有
            } else {
                Err(error)
            }
        } else {
            Ok(true)
        }
    }
    
    /// 获取当前锁定的进程ID
    pub fn get_locked_pid(&self) -> Option<u32> {
        if self.lock_file_path.exists() {
            let mut file = File::open(&self.lock_file_path).ok()?;
            let mut content = String::new();
            file.read_to_string(&mut content).ok()?;
            content.trim().parse().ok()
        } else {
            None
        }
    }
}

impl Drop for ProcessLock {
    fn drop(&mut self) {
        if let Err(e) = self.release() {
            error!("释放进程锁时出错: {}", e);
        }
    }
}

/// 守护进程单例管理器
pub struct DaemonSingleton {
    process_lock: ProcessLock,
}

impl DaemonSingleton {
    /// 创建守护进程单例管理器
    pub fn new() -> Self {
        Self {
            process_lock: ProcessLock::new("secure-password-daemon"),
        }
    }
    
    /// 尝试获取守护进程单例锁
    pub fn try_acquire(&mut self) -> Result<bool, io::Error> {
        self.process_lock.try_lock()
    }
    
    /// 释放守护进程单例锁
    pub fn release(&mut self) -> Result<(), io::Error> {
        self.process_lock.release()
    }
    
    /// 检查是否有其他守护进程实例正在运行
    pub fn check_existing_instance(&self) -> Option<u32> {
        self.process_lock.get_locked_pid()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    
    #[tokio::test]
    async fn test_process_lock_basic() {
        let mut lock1 = ProcessLock::new("test-daemon");
        
        // 第一个锁应该成功
        assert!(lock1.try_lock().unwrap());
        
        // 第二个锁应该失败
        let mut lock2 = ProcessLock::new("test-daemon");
        assert!(!lock2.try_lock().unwrap());
        
        // 释放第一个锁
        lock1.release().unwrap();
        
        // 现在第二个锁应该成功
        assert!(lock2.try_lock().unwrap());
        
        lock2.release().unwrap();
    }
    
    #[tokio::test]
    async fn test_daemon_singleton() {
        let mut singleton1 = DaemonSingleton::new();
        assert!(singleton1.try_acquire().unwrap());
        
        let mut singleton2 = DaemonSingleton::new();
        assert!(!singleton2.try_acquire().unwrap());
        
        singleton1.release().unwrap();
        assert!(singleton2.try_acquire().unwrap());
        
        singleton2.release().unwrap();
    }
} 