//! # 日志系统模块
//! 
//! 提供统一的日志配置和管理功能

use std::fs;
use std::io::{self, IsTerminal};
use std::path::Path;
use tracing::{Level, info};
use tracing_subscriber::{
    layer::SubscriberExt,
    util::SubscriberInitExt,
    fmt,
    filter::EnvFilter,
};
use anyhow::Result;

/// 初始化日志系统
pub fn init_logging() -> Result<()> {
    // 检查是否已经初始化
    if tracing::dispatcher::has_been_set() {
        return Ok(());
    }

    // 检查是否在Native Messaging模式
    let is_native_messaging = std::env::args().any(|arg| arg == "--native-messaging");
    
    let log_level = std::env::var("RUST_LOG")
        .unwrap_or_else(|_| "info".to_string());
    
    // 创建环境过滤器
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(&log_level));

    if is_native_messaging {
        // Native Messaging模式：日志输出到stderr，不污染stdout
        let stderr_layer = fmt::layer()
            .with_writer(io::stderr)
            .with_ansi(false) // 禁用颜色输出
            .compact();

        tracing_subscriber::registry()
            .with(env_filter)
            .with(stderr_layer)
            .init();
            
        eprintln!("日志系统已初始化 (Native Messaging模式，输出到stderr)");
    } else {
        // 普通模式：始终使用compact格式避免类型不匹配
        let stdout_layer = fmt::layer()
            .with_writer(io::stdout)
            .with_ansi(IsTerminal::is_terminal(&io::stdout()))
            .compact();

        tracing_subscriber::registry()
            .with(env_filter)
            .with(stdout_layer)
            .init();
            
        info!("日志系统已初始化 (普通模式)");
    }

    Ok(())
}

/// 初始化文件日志系统
pub fn init_file_logging<P: AsRef<Path>>(log_file: P) -> Result<()> {
    // 检查是否已经初始化
    if tracing::dispatcher::has_been_set() {
        return Ok(());
    }

    // 确保日志目录存在
    if let Some(parent) = log_file.as_ref().parent() {
        fs::create_dir_all(parent)?;
    }

    let log_level = std::env::var("RUST_LOG")
        .unwrap_or_else(|_| "info".to_string());
    
    // 创建环境过滤器
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(&log_level));

    // 创建文件写入器
    let file = fs::OpenOptions::new()
        .create(true)
        .write(true)
        .append(true)
        .open(&log_file)?;

    let file_layer = fmt::layer()
        .with_writer(file)
        .with_ansi(false)
        .compact();

    tracing_subscriber::registry()
        .with(env_filter)
        .with(file_layer)
        .init();

    info!("文件日志系统已初始化: {:?}", log_file.as_ref());
    Ok(())
}

/// 获取默认日志级别
pub fn get_default_log_level() -> Level {
    match std::env::var("RUST_LOG").as_deref() {
        Ok("trace") => Level::TRACE,
        Ok("debug") => Level::DEBUG,
        Ok("info") => Level::INFO,
        Ok("warn") => Level::WARN,
        Ok("error") => Level::ERROR,
        _ => Level::INFO,
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[test]
    fn test_get_default_log_level() {
        assert_eq!(get_default_log_level(), Level::INFO);
    }

    #[tokio::test]
    async fn test_file_logging() {
        let temp_file = NamedTempFile::new().unwrap();
        let log_path = temp_file.path();
        
        // 注意：这个测试在真实环境中可能失败，因为日志系统只能初始化一次
        // 在测试中我们跳过实际的初始化
        assert!(log_path.exists());
    }
}

/// 初始化 JSON 格式日志
pub fn init_json_logging(log_file: Option<&str>) -> Result<()> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));

    match log_file {
        Some(file_path) => {
            use std::fs::OpenOptions;
            use tracing_subscriber::fmt::writer::MakeWriterExt;

            let file = OpenOptions::new()
                .create(true)
                .append(true)
                .open(file_path)?;

            let file_layer = fmt::layer()
                .json()
                .with_writer(file.with_max_level(tracing::Level::TRACE))
                .with_current_span(true)
                .with_span_list(true);

            tracing_subscriber::registry()
                .with(env_filter)
                .with(file_layer)
                .init();
        }
        None => {
            let console_layer = fmt::layer()
                .json()
                .with_current_span(true)
                .with_span_list(true);

            tracing_subscriber::registry()
                .with(env_filter)
                .with(console_layer)
                .init();
        }
    }

    Ok(())
}

/// 日志轮转管理器
pub struct LogRotationManager {
    /// 日志文件路径
    log_file_path: String,
    /// 最大文件大小 (字节)
    max_file_size: u64,
    /// 保留的日志文件数量
    max_files: u32,
}

impl LogRotationManager {
    /// 创建新的日志轮转管理器
    pub fn new(log_file_path: String, max_file_size: u64, max_files: u32) -> Self {
        Self {
            log_file_path,
            max_file_size,
            max_files,
        }
    }

    /// 检查是否需要轮转
    pub fn should_rotate(&self) -> Result<bool> {
        use std::fs;

        match fs::metadata(&self.log_file_path) {
            Ok(metadata) => Ok(metadata.len() > self.max_file_size),
            Err(_) => Ok(false), // 文件不存在，不需要轮转
        }
    }

    /// 执行日志轮转
    pub fn rotate(&self) -> Result<()> {
        use std::fs;
        use std::path::Path;

        let log_path = Path::new(&self.log_file_path);

        // 轮转现有文件
        for i in (1..self.max_files).rev() {
            let old_file = format!("{}.{}", self.log_file_path, i);
            let new_file = format!("{}.{}", self.log_file_path, i + 1);

            if Path::new(&old_file).exists() {
                if i == self.max_files - 1 {
                    // 删除最老的文件
                    fs::remove_file(&old_file)?;
                } else {
                    // 重命名文件
                    fs::rename(&old_file, &new_file)?;
                }
            }
        }

        // 重命名当前日志文件
        if log_path.exists() {
            let backup_file = format!("{}.1", self.log_file_path);
            fs::rename(&self.log_file_path, &backup_file)?;
        }

        Ok(())
    }
}

/// 性能监控日志
pub struct PerformanceLogger {
    /// 开始时间
    start_time: std::time::Instant,
    /// 操作名称
    operation_name: String,
}

impl PerformanceLogger {
    /// 开始性能监控
    pub fn start(operation_name: &str) -> Self {
        Self {
            start_time: std::time::Instant::now(),
            operation_name: operation_name.to_string(),
        }
    }

    /// 结束性能监控并记录日志
    pub fn finish(self) {
        let duration = self.start_time.elapsed();
        tracing::info!(
            operation = %self.operation_name,
            duration_ms = duration.as_millis(),
            "操作完成"
        );
    }

    /// 记录中间检查点
    pub fn checkpoint(&self, checkpoint_name: &str) {
        let duration = self.start_time.elapsed();
        tracing::debug!(
            operation = %self.operation_name,
            checkpoint = checkpoint_name,
            duration_ms = duration.as_millis(),
            "检查点"
        );
    }
}

/// 结构化日志宏
#[macro_export]
macro_rules! log_operation {
    ($level:ident, $operation:expr, $($field:ident = $value:expr),*) => {
        tracing::$level!(
            operation = $operation,
            $($field = $value,)*
        );
    };
}

/// 错误日志宏
#[macro_export]
macro_rules! log_error {
    ($error:expr, $context:expr) => {
        tracing::error!(
            error = %$error,
            context = $context,
            "操作失败"
        );
    };
}

/// 审计日志记录器
pub struct AuditLogger;

impl AuditLogger {
    /// 记录用户操作
    pub fn log_user_action(user_id: &str, action: &str, resource: &str, result: &str) {
        tracing::info!(
            event_type = "user_action",
            user_id = user_id,
            action = action,
            resource = resource,
            result = result,
            timestamp = chrono::Utc::now().to_rfc3339(),
            "用户操作审计"
        );
    }

    /// 记录系统事件
    pub fn log_system_event(event_type: &str, details: &str) {
        tracing::info!(
            event_type = "system_event",
            event = event_type,
            details = details,
            timestamp = chrono::Utc::now().to_rfc3339(),
            "系统事件审计"
        );
    }

    /// 记录安全事件
    pub fn log_security_event(event_type: &str, source: &str, details: &str) {
        tracing::warn!(
            event_type = "security_event",
            security_event = event_type,
            source = source,
            details = details,
            timestamp = chrono::Utc::now().to_rfc3339(),
            "安全事件审计"
        );
    }
}

