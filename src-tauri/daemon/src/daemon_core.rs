//! 守护进程核心逻辑模块
//!
//! 实现 SecurePasswordDaemon 主结构和生命周期管理

use std::sync::Arc;
use tokio::sync::{Notify, RwLock};
use tracing::{info, error};
use anyhow::Result;

use crate::config::DaemonConfig;
use crate::error::DaemonError;
use crate::ipc::{IpcServer, IpcMessage, IpcResponse, IpcResult};
use crate::ipc::server::{MessageHandler, ServerEvent};
use crate::native_messaging::{NativeMessagingHost, HostConfig};
use crate::app_manager::{AppLifecycleManager, AppConfig};

/// 守护进程运行模式
#[derive(Debug, Clone, PartialEq)]
pub enum RuntimeMode {
    /// Windows 系统服务
    WindowsService,
    /// macOS LaunchDaemon
    MacOSLaunchDaemon,
    /// Linux systemd 服务
    LinuxSystemd,
    /// 交互模式 (开发和调试)
    Interactive,
}

/// 守护进程状态
#[derive(Debug, Clone, PartialEq)]
pub enum DaemonStatus {
    /// 未启动
    NotStarted,
    /// 启动中
    Starting,
    /// 运行中
    Running,
    /// 关闭中
    Stopping,
    /// 已停止
    Stopped,
    /// 错误状态
    Error(String),
}

/// 守护进程主结构
pub struct SecurePasswordDaemon {
    /// 配置
    config: DaemonConfig,
    /// 运行模式
    runtime_mode: RuntimeMode,
    /// 当前状态
    status: DaemonStatus,
    /// 关闭信号
    shutdown_signal: Arc<Notify>,
    /// IPC 服务器
    ipc_server: Arc<RwLock<Option<IpcServer>>>,
    /// Native Messaging Host
    native_messaging_host: Arc<RwLock<Option<NativeMessagingHost>>>,
    /// 应用生命周期管理器
    app_manager: Arc<RwLock<Option<AppLifecycleManager>>>,
}

impl SecurePasswordDaemon {
    /// 创建新的守护进程实例
    pub async fn new(config: DaemonConfig) -> Result<Self, DaemonError> {
        info!("创建守护进程实例");

        // 检测运行模式
        let runtime_mode = Self::detect_runtime_mode();
        info!("检测到运行模式: {:?}", runtime_mode);

        Ok(Self {
            config,
            runtime_mode,
            status: DaemonStatus::NotStarted,
            shutdown_signal: Arc::new(Notify::new()),
            ipc_server: Arc::new(RwLock::new(None)),
            native_messaging_host: Arc::new(RwLock::new(None)),
            app_manager: Arc::new(RwLock::new(None)),
        })
    }
    
    /// 启动守护进程
    pub async fn start(&mut self) -> Result<(), DaemonError> {
        info!("启动守护进程");
        self.status = DaemonStatus::Starting;

        // 1. 验证配置
        self.config.validate()
            .map_err(|e| DaemonError::ConfigValidationError(e.to_string()))?;

        // 2. 初始化日志系统
        self.initialize_logging().await?;

        // 3. 初始化各个子模块
        self.initialize_modules().await?;

        // 4. 启动 IPC 服务器
        self.start_ipc_server().await?;

        // 5. 启动 Native Messaging Host
        self.start_native_messaging_host().await?;

        // 6. 启动应用管理器
        self.start_app_manager().await?;

        // 7. 启动安全代理
        self.start_security_proxy().await?;

        // 8. 启动监控系统
        self.start_monitoring_system().await?;

        self.status = DaemonStatus::Running;
        info!("守护进程启动完成");

        Ok(())
    }
    
    /// 优雅关闭守护进程
    pub async fn shutdown(&mut self) -> Result<(), DaemonError> {
        info!("开始关闭守护进程");
        self.status = DaemonStatus::Stopping;

        // 发送关闭信号
        self.shutdown_signal.notify_waiters();

        // 1. 停止监控系统
        self.stop_monitoring_system().await?;

        // 2. 停止安全代理
        self.stop_security_proxy().await?;

        // 3. 停止应用管理器
        self.stop_app_manager().await?;

        // 4. 停止 Native Messaging Host
        self.stop_native_messaging_host().await?;

        // 5. 停止 IPC 服务器
        self.stop_ipc_server().await?;

        // 6. 清理资源
        self.cleanup_resources().await?;

        self.status = DaemonStatus::Stopped;
        info!("守护进程关闭完成");

        Ok(())
    }
    
    /// 重新加载配置
    pub async fn reload_config(&mut self, config: DaemonConfig) -> Result<(), DaemonError> {
        info!("重新加载配置");
        
        // TODO: 实现配置热重载
        self.config = config;
        
        Ok(())
    }
    
    /// 获取守护进程状态
    pub fn get_status(&self) -> DaemonStatus {
        self.status.clone()
    }
    
    /// 检测运行模式
    fn detect_runtime_mode() -> RuntimeMode {
        #[cfg(windows)]
        {
            // 检测是否作为 Windows 服务运行
            if std::env::var("RUNNING_AS_SERVICE").is_ok() {
                RuntimeMode::WindowsService
            } else {
                RuntimeMode::Interactive
            }
        }

        #[cfg(target_os = "macos")]
        {
            // 检测是否作为 LaunchDaemon 运行
            if std::env::var("LAUNCHED_BY_LAUNCHD").is_ok() {
                RuntimeMode::MacOSLaunchDaemon
            } else {
                RuntimeMode::Interactive
            }
        }

        #[cfg(target_os = "linux")]
        {
            // 检测是否作为 systemd 服务运行
            if std::env::var("INVOCATION_ID").is_ok() || std::env::var("SYSTEMD_EXEC_PID").is_ok() {
                RuntimeMode::LinuxSystemd
            } else {
                RuntimeMode::Interactive
            }
        }

        #[cfg(not(any(windows, target_os = "macos", target_os = "linux")))]
        {
            RuntimeMode::Interactive
        }
    }

    /// 初始化日志系统
    async fn initialize_logging(&self) -> Result<(), DaemonError> {
        info!("初始化日志系统");
        // TODO: 根据配置初始化日志
        Ok(())
    }

    /// 初始化各个子模块
    async fn initialize_modules(&self) -> Result<(), DaemonError> {
        info!("初始化子模块");
        // TODO: 初始化各个子模块
        Ok(())
    }

    /// 启动 IPC 服务器
    async fn start_ipc_server(&self) -> Result<(), DaemonError> {
        info!("启动 IPC 服务器");

        // 创建 IPC 服务器配置
        let server_config = crate::ipc::ServerConfig {
            bind_address: self.config.ipc.bind_address.clone(),
            port: self.config.ipc.port,
            max_connections: self.config.ipc.max_connections,
            connection_timeout_ms: self.config.ipc.connection_timeout * 1000,
            message_timeout_ms: 10000, // 10秒消息超时
            heartbeat_interval_ms: 30000, // 30秒心跳间隔
            transport_config: crate::ipc::transport::TransportConfig {
                transport_type: match self.config.ipc.transport {
                    crate::config::IpcTransportType::Tcp => crate::ipc::transport::TransportType::Tcp,
                    crate::config::IpcTransportType::UnixSocket => crate::ipc::transport::TransportType::UnixSocket,
                    crate::config::IpcTransportType::NamedPipe => crate::ipc::transport::TransportType::NamedPipe,
                    crate::config::IpcTransportType::Auto => {
                        // 根据平台自动选择
                        #[cfg(windows)]
                        { crate::ipc::transport::TransportType::NamedPipe }
                        #[cfg(unix)]
                        { crate::ipc::transport::TransportType::UnixSocket }
                        #[cfg(not(any(windows, unix)))]
                        { crate::ipc::transport::TransportType::Tcp }
                    }
                },
                address: self.config.ipc.bind_address.clone(),
                port: self.config.ipc.port,
                timeout_ms: self.config.ipc.connection_timeout * 1000,
                max_message_size: 10 * 1024 * 1024, // 10MB
                buffer_size: 8192,
            },
        };

        // 创建消息处理器
        let message_handler = Arc::new(DaemonMessageHandler::new());

        // 创建并启动 IPC 服务器
        let mut ipc_server = IpcServer::new(server_config)
            .with_message_handler(message_handler);

        // 在后台任务中启动 IPC 服务器，避免阻塞主线程
        let _ipc_handle = tokio::spawn(async move {
            if let Err(e) = ipc_server.start().await {
                error!("IPC 服务器运行失败: {}", e);
            }
        });

        // 给服务器一点时间启动
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        info!("IPC 服务器启动成功");
        Ok(())
    }

    /// 启动 Native Messaging Host
    async fn start_native_messaging_host(&self) -> Result<(), DaemonError> {
        info!("启动 Native Messaging Host");

        if !self.config.native_messaging.enabled {
            info!("Native Messaging 已禁用，跳过启动");
            return Ok(());
        }

        // 创建 Native Messaging Host 配置
        let host_config = HostConfig {
            host_name: self.config.native_messaging.host_name.clone(),
            description: "Secure Password Native Messaging Host".to_string(),
            executable_path: std::env::current_exe()
                .unwrap_or_else(|_| "secure-password-daemon".into())
                .to_string_lossy()
                .to_string(),
            allowed_origins: vec![
                "chrome-extension://secure-password-extension/".to_string(),
                "moz-extension://secure-password-extension/".to_string(),
            ],
            allowed_extensions: self.config.native_messaging.extension_whitelist.clone(),
            ipc_config: crate::ipc::ClientConfig {
                server_address: self.config.ipc.bind_address.clone(),
                port: self.config.ipc.port,
                timeout_ms: self.config.ipc.connection_timeout * 1000,
                request_timeout_ms: 10000,
                reconnect_interval_ms: 1000,
                max_reconnect_attempts: 3,
                heartbeat_interval_ms: 30000,
                auto_reconnect: true,
                transport_type: match self.config.ipc.transport {
                    crate::config::IpcTransportType::Tcp => crate::ipc::transport::TransportType::Tcp,
                    crate::config::IpcTransportType::UnixSocket => crate::ipc::transport::TransportType::UnixSocket,
                    crate::config::IpcTransportType::NamedPipe => crate::ipc::transport::TransportType::NamedPipe,
                    crate::config::IpcTransportType::Auto => {
                        #[cfg(windows)]
                        { crate::ipc::transport::TransportType::NamedPipe }
                        #[cfg(unix)]
                        { crate::ipc::transport::TransportType::UnixSocket }
                        #[cfg(not(any(windows, unix)))]
                        { crate::ipc::transport::TransportType::Tcp }
                    }
                },
                message_buffer_size: 8192,
            },
            protocol_version: crate::native_messaging::protocol::ProtocolVersion::V2,
            request_timeout: std::time::Duration::from_secs(30),
            max_concurrent_requests: 100,
            debug_mode: false,
            heartbeat_interval: std::time::Duration::from_secs(30),
            verify_signatures: false,
            signature_key: None,
        };

        // 创建并启动 Native Messaging Host
        let mut host = NativeMessagingHost::new(host_config)
            .await
            .map_err(|e| DaemonError::NativeMessagingStartError(format!("创建Native Messaging Host失败: {}", e)))?;

        // 在后台任务中启动 Host
        let _host_handle = tokio::spawn(async move {
            if let Err(e) = host.start().await {
                error!("Native Messaging Host 运行失败: {}", e);
            }
        });

        // 保存 Host 实例（这里我们保存的是 handle，实际使用中可能需要调整）
        // 由于 Host 在后台运行，我们暂时不保存实例

        info!("Native Messaging Host 启动成功");
        Ok(())
    }

    /// 启动应用管理器
    async fn start_app_manager(&self) -> Result<(), DaemonError> {
        info!("启动应用管理器");

        // 创建应用配置
        let app_config = AppConfig {
            app_path: std::path::PathBuf::from(&self.config.app_manager.app_path),
            working_directory: None,
            startup_args: vec!["--daemon-mode".to_string()],
            startup_timeout: self.config.app_manager.startup_timeout,
            health_check_interval: self.config.app_manager.health_check_interval,
            max_restart_attempts: self.config.app_manager.max_restart_attempts,
            restart_delay: 5, // 5秒重启延迟
            ipc_port: self.config.ipc.port.unwrap_or(8080) + 1, // 使用不同的端口避免冲突
            silent_mode: true,
            memory_limit_mb: Some(500),
            cpu_limit_percent: Some(80.0),
        };

        // 创建应用生命周期管理器
        let app_manager = AppLifecycleManager::new(app_config);

        // 启动应用管理器
        app_manager.start_lifecycle_management()
            .await
            .map_err(|e| DaemonError::AppStartError(format!("启动应用管理器失败: {}", e)))?;

        // 保存管理器实例
        *self.app_manager.write().await = Some(app_manager);

        info!("应用管理器启动成功");
        Ok(())
    }

    /// 启动安全代理
    async fn start_security_proxy(&self) -> Result<(), DaemonError> {
        info!("启动安全代理");
        // TODO: 启动安全代理
        Ok(())
    }

    /// 启动监控系统
    async fn start_monitoring_system(&self) -> Result<(), DaemonError> {
        info!("启动监控系统");
        // TODO: 启动监控系统
        Ok(())
    }

    /// 停止监控系统
    async fn stop_monitoring_system(&self) -> Result<(), DaemonError> {
        info!("停止监控系统");
        // TODO: 停止监控系统
        Ok(())
    }

    /// 停止安全代理
    async fn stop_security_proxy(&self) -> Result<(), DaemonError> {
        info!("停止安全代理");
        // TODO: 停止安全代理
        Ok(())
    }

    /// 停止应用管理器
    async fn stop_app_manager(&self) -> Result<(), DaemonError> {
        info!("停止应用管理器");

        if let Some(manager) = self.app_manager.write().await.take() {
            manager.stop_lifecycle_management()
                .await
                .map_err(|e| DaemonError::AppStopError(format!("停止应用管理器失败: {}", e)))?;
        }

        info!("应用管理器已停止");
        Ok(())
    }

    /// 停止 Native Messaging Host
    async fn stop_native_messaging_host(&self) -> Result<(), DaemonError> {
        info!("停止 Native Messaging Host");
        // TODO: 停止 Native Messaging Host
        Ok(())
    }

    /// 停止 IPC 服务器
    async fn stop_ipc_server(&self) -> Result<(), DaemonError> {
        info!("停止 IPC 服务器");

        // 由于IPC服务器在后台任务中运行，我们通过关闭信号来停止它
        // 实际的停止逻辑会在守护进程的整体关闭流程中处理

        info!("IPC 服务器停止信号已发送");
        Ok(())
    }

    /// 清理资源
    async fn cleanup_resources(&self) -> Result<(), DaemonError> {
        info!("清理资源");
        // TODO: 清理资源
        Ok(())
    }
}

/// 守护进程消息处理器
pub struct DaemonMessageHandler {
    // 可以添加需要的字段，比如应用管理器的引用等
}

impl DaemonMessageHandler {
    pub fn new() -> Self {
        Self {}
    }
}

#[async_trait::async_trait]
impl MessageHandler for DaemonMessageHandler {
    /// 处理消息
    async fn handle_message(
        &self,
        connection_id: &str,
        message: IpcMessage,
    ) -> IpcResult<Option<IpcResponse>> {
        info!("处理来自连接 {} 的消息: {:?}", connection_id, message.message_type);

        match message.message_type {
            crate::ipc::IpcMessageType::Ping => {
                // 处理心跳消息
                Ok(Some(IpcResponse {
                    request_id: message.message_id,
                    status: crate::ipc::protocol::ResponseStatus::Success,
                    data: serde_json::json!({"pong": true}),
                    error: None,
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs(),
                    source: "daemon".to_string(),
                }))
            }
            crate::ipc::IpcMessageType::BrowserRequest => {
                // 处理来自浏览器的请求
                // 这里应该转发到 Tauri 主应用
                info!("收到浏览器请求，转发到主应用");

                // 暂时返回一个简单的响应
                Ok(Some(IpcResponse {
                    request_id: message.message_id,
                    status: crate::ipc::protocol::ResponseStatus::Success,
                    data: serde_json::json!({"message": "请求已处理"}),
                    error: None,
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs(),
                    source: "daemon".to_string(),
                }))
            }
            _ => {
                // 其他消息类型
                Ok(Some(IpcResponse {
                    request_id: message.message_id,
                    status: crate::ipc::protocol::ResponseStatus::Error,
                    data: serde_json::json!({}),
                    error: Some("不支持的消息类型".to_string()),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs(),
                    source: "daemon".to_string(),
                }))
            }
        }
    }

    /// 处理连接事件
    async fn handle_connection_event(&self, event: ServerEvent) -> IpcResult<()> {
        match event {
            ServerEvent::ClientConnected { connection_id, remote_addr } => {
                info!("客户端连接: {} ({})", connection_id, remote_addr.unwrap_or_default());
            }
            ServerEvent::ClientDisconnected { connection_id, reason } => {
                info!("客户端断开: {} ({})", connection_id, reason);
            }
            ServerEvent::MessageReceived { connection_id, message } => {
                info!("收到消息: {} -> {:?}", connection_id, message.message_type);
            }
            ServerEvent::MessageSent { connection_id, message } => {
                info!("发送响应: {} -> {:?}", connection_id, message.status);
            }
            ServerEvent::ServerError { error } => {
                error!("服务器错误: {}", error);
            }
        }
        Ok(())
    }
}
