#!/usr/bin/env python3
"""
简化的Native Messaging Host测试
使用标准Chrome Native Messaging协议格式
"""

import json
import struct
import subprocess
import sys
import time
import os

def send_message(process, message):
    """向Native Messaging Host发送消息"""
    message_json = json.dumps(message)
    message_bytes = message_json.encode('utf-8')
    length = len(message_bytes)
    length_bytes = struct.pack('<I', length)
    
    print(f"📤 发送消息 (长度: {length}): {message_json}")
    
    try:
        process.stdin.write(length_bytes)
        process.stdin.write(message_bytes)
        process.stdin.flush()
        return True
    except Exception as e:
        print(f"❌ 发送消息失败: {e}")
        return False

def read_message(process):
    """从Native Messaging Host读取消息"""
    try:
        # 读取长度前缀（4字节）
        length_bytes = process.stdout.read(4)
        if len(length_bytes) != 4:
            print(f"❌ 无法读取长度前缀，只读取到 {len(length_bytes)} 字节")
            return None
        
        # 解析长度
        length = struct.unpack('<I', length_bytes)[0]
        print(f"📥 接收消息长度: {length}")
        
        if length > 1024 * 1024:  # 1MB限制
            print(f"❌ 消息过大: {length} bytes")
            return None
            
        # 读取消息内容
        message_bytes = process.stdout.read(length)
        if len(message_bytes) != length:
            print(f"❌ 期望读取 {length} 字节，实际读取 {len(message_bytes)} 字节")
            return None
        
        # 解析JSON
        try:
            message = json.loads(message_bytes.decode('utf-8'))
            print(f"📥 接收消息内容: {json.dumps(message, indent=2)}")
            return message
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"原始数据: {message_bytes}")
            return None
            
    except Exception as e:
        print(f"❌ 读取消息时出错: {e}")
        return None

def test_simple_ping():
    """测试简单的ping消息"""
    daemon_path = "/Users/<USER>/Documents/tarui/secure-password/src-tauri/target/release/secure-password-daemon"
    
    if not os.path.exists(daemon_path):
        print(f"❌ 守护进程文件不存在: {daemon_path}")
        return False
    
    print("🚀 启动Native Messaging Host...")
    
    try:
        # 启动守护进程
        process = subprocess.Popen(
            [daemon_path, "--native-messaging", "--log-level", "debug"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd="/Users/<USER>/Documents/tarui/secure-password/src-tauri/daemon"
        )
        
        print("⏳ 等待进程启动...")
        time.sleep(2)  # 给进程一些时间启动
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ 进程已退出，返回码: {process.returncode}")
            print(f"标准输出: {stdout.decode('utf-8', errors='ignore')}")
            print(f"标准错误: {stderr.decode('utf-8', errors='ignore')}")
            return False
        
        print("✅ 进程启动成功，发送ping消息...")
        
        # 发送简单的ping消息 (使用Chrome兼容格式)
        ping_message = {
            "version": 2,
            "message_type": "ping",
            "request_id": "test-ping-001",
            "timestamp": int(time.time()),
            "source": "test-extension",
            "payload": {},
            "extensions": {
                "extension_id": "test-extension-id"
            }
        }
        
        if not send_message(process, ping_message):
            return False
        
        print("⏳ 等待响应...")
        
        # 设置读取超时
        import select
        ready, _, _ = select.select([process.stdout], [], [], 10.0)
        if not ready:
            print("⏰ 读取响应超时")
            return False
        
        response = read_message(process)
        
        if response:
            if response.get("message_type") == "pong":
                print("✅ 测试成功! 收到正确的pong响应")
                return True
            else:
                print(f"❌ 响应类型不正确，期望 'pong'，实际: {response.get('message_type')}")
                return False
        else:
            print("❌ 未收到有效响应")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    finally:
        try:
            if process.poll() is None:
                print("🧹 清理进程...")
                process.terminate()
                process.wait(timeout=5)
        except:
            pass

def main():
    print("=" * 60)
    print("Native Messaging Host 简化测试")
    print("=" * 60)
    
    success = test_simple_ping()
    
    print("=" * 60)
    if success:
        print("🎉 所有测试通过!")
    else:
        print("💥 测试失败!")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main()) 