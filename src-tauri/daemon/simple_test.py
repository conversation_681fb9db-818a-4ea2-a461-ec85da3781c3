#!/usr/bin/env python3
"""
简单的Native Messaging Host测试
"""

import json
import struct
import subprocess
import sys
import time

def send_message(process, message):
    """向Native Messaging Host发送消息"""
    message_json = json.dumps(message)
    message_bytes = message_json.encode('utf-8')
    length = len(message_bytes)
    length_bytes = struct.pack('<I', length)
    
    print(f"发送: {message_json}")
    process.stdin.write(length_bytes)
    process.stdin.write(message_bytes)
    process.stdin.flush()

def read_message(process):
    """从Native Messaging Host读取消息"""
    length_bytes = process.stdout.read(4)
    if len(length_bytes) != 4:
        return None
    
    length = struct.unpack('<I', length_bytes)[0]
    message_bytes = process.stdout.read(length)
    if len(message_bytes) != length:
        return None
    
    try:
        message = json.loads(message_bytes.decode('utf-8'))
        print(f"接收: {json.dumps(message, indent=2)}")
        return message
    except json.JSONDecodeError:
        return None

def main():
    daemon_path = "/Users/<USER>/Documents/tarui/secure-password/src-tauri/target/release/secure-password-daemon"
    
    print("启动Native Messaging Host...")
    process = subprocess.Popen(
        [daemon_path, "--native-messaging"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    time.sleep(1)  # 等待启动
    
    # 发送ping消息
    ping_message = {
        "version": "2.0",
        "message_type": "ping",
        "request_id": "test-001",
        "timestamp": int(time.time()),
        "source": "test-extension",
        "payload": {},
        "extensions": {
            "extension_id": "test-extension-id"
        }
    }
    
    try:
        send_message(process, ping_message)
        response = read_message(process)
        
        if response and response.get("message_type") == "pong":
            print("✅ 测试成功!")
        else:
            print("❌ 测试失败!")
            
    except Exception as e:
        print(f"错误: {e}")
    finally:
        process.terminate()

if __name__ == "__main__":
    main()
